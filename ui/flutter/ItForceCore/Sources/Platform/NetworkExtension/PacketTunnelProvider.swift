/**
 * FILE: PacketTunnelProvider.swift
 *
 * DESCRIPTION:
 *     Core NetworkExtension PacketTunnelProvider implementation for iOS/macOS VPN functionality.
 *     Integrates SDWAN protocol with system VPN framework, handles tunnel lifecycle management,
 *     and provides seamless integration with ConnectionManager and TUN device management.
 *
 *     Key Features:
 *     - NetworkExtension integration with iOS/macOS system VPN
 *     - SDWAN protocol connection management
 *     - Automatic packet processing via ConnectionManager
 *     - Runtime configuration updates (DNS, MTU)
 *     - Server switching support (requires tunnel restart)
 *     - Comprehensive error handling and recovery
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create NetworkExtension PacketTunnelProvider with SDWAN protocol integration
 */

import NetworkExtension
import Foundation
import CoreFoundation

/**
 * NAME: VPNPacketTunnelProvider
 *
 * DESCRIPTION:
 *     Main PacketTunnelProvider implementation that integrates with iOS/macOS system VPN.
 *     Manages tunnel establishment, SDWAN protocol processing, and system integration.
 *     Provides bridge between NetworkExtension framework and ItForceCore VPN services.
 *
 * PROPERTIES:
 *     connectionManager - SDWAN protocol connection management
 *     logger - Structured logging system
 *     isStarted - Tunnel running state
 *     configuration - Current tunnel configuration
 *
 * NOTE: Simplified architecture - removed RouteManager and DNSManager
 *       NetworkExtension APIs (NEIPv4Settings, NEDNSSettings) handle all routing and DNS configuration
 */
open class VPNPacketTunnelProvider: NEPacketTunnelProvider {
    
    // MARK: - Properties

    private var connectionManager: ConnectionManager?
    private let logger: LoggerProtocol
    
    private var isStarted: Bool = false
    private var configuration: VPNTunnelConfiguration?
    private var packetProcessingTask: Task<Void, Never>?
    private var isReconnecting: Bool = false

    // MARK: - VPN Extension Communication Properties

    private var notificationObserver: VPNExtensionNotificationObserver?
    private var serverManager: ServerManager?
    
    // MARK: - Initialization
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Initializes PacketTunnelProvider with required dependencies.
     *     Sets up logging and prepares for tunnel operations.
     */
    public override init() {
        // Initialize logger for NetworkExtension environment
        self.logger = OSLogLogger.createNetworkExtensionLogger(category: "PacketTunnelProvider")

        super.init()

        logger.info("VPNPacketTunnelProvider initialized")

        // 🔧 CRITICAL FIX: 立即设置Darwin Notifications监听
        // 这样VPN Extension一启动就能接收主应用的ping请求
        setupDarwinNotifications()
        logger.info("VPNPacketTunnelProvider: Darwin Notifications setup completed in init")
    }
    
    // MARK: - NetworkExtension Lifecycle
    
    /**
     * NAME: startTunnel
     *
     * DESCRIPTION:
     *     Starts the VPN tunnel with provided configuration options.
     *     Configures network settings, establishes SDWAN connection, and begins packet processing.
     *     Equivalent to Go backend's main connection establishment flow.
     *
     * PARAMETERS:
     *     options - Tunnel configuration options from system/app
     *     completionHandler - Completion callback with result
     */
    public override func startTunnel(options: [String : NSObject]?,
                                   completionHandler: @escaping (Error?) -> Void) {
        // Use NSLog for immediate visibility in system logs
        // NSLog("startTunnel called with options: \(options?.description ?? "nil")") // Debug NSLog commented for production
        logger.info("Starting VPN tunnel", metadata: ["options_count": "\(options?.count ?? 0)"])



        Task {
            do {
                // NSLog("Step 1: Parsing configuration from options") // Debug NSLog commented for production
                logger.info("Step 1: Parsing configuration from options")
                // Parse configuration from options
                let config = try parseConfiguration(from: options)
                self.configuration = config
                // NSLog("Step 1 completed: server=\(config.serverAddress), port=\(config.serverPort), username=\(config.username)") // Debug NSLog commented for production
                logger.info("Step 1 completed: Configuration parsed successfully", metadata: [
                    "server": config.serverAddress,
                    "port": "\(config.serverPort)",
                    "username": config.username
                ])

                // NSLog("Step 2: Initializing core components") // Debug NSLog commented for production
                logger.info("Step 2: Initializing core components")
                // Initialize core components
                try await initializeComponents(config: config)
                // NSLog("Step 2 completed: Core components initialized") // Debug NSLog commented for production
                logger.info("Step 2 completed: Core components initialized")

                // NSLog("Step 3: Starting SDWAN connection and authentication") // Debug NSLog commented for production
                logger.info("Step 3: Starting SDWAN connection and authentication")
                // Start SDWAN connection (includes authentication and data connection)
                let serverConfig = try await startSDWANConnection(config: config)
                // NSLog("Step 3 completed: SDWAN connection established with authentication") // Debug NSLog commented for production
                logger.info("Step 3 completed: SDWAN connection established with authentication")

                // NSLog("Step 4: Configuring VPN network settings with server parameters") // Debug NSLog commented for production
                logger.info("Step 4: Configuring VPN network settings with server parameters")
                // Configure network settings using server configuration from OpenAck response
                let networkSettings = try createNetworkSettings(for: config, serverConfig: serverConfig)
                try await setTunnelNetworkSettings(networkSettings)
                // NSLog("Step 4 completed: VPN network settings configured") // Debug NSLog commented for production
                logger.info("Step 4 completed: VPN network settings configured")

                isStarted = true
                // NSLog("VPN tunnel started successfully - all steps completed") // Debug NSLog commented for production
                logger.info("VPN tunnel started successfully - all steps completed")
                completionHandler(nil)

            } catch {
                // NSLog("Failed to start VPN tunnel: \(error.localizedDescription)") // Debug NSLog commented for production
                logger.error("Failed to start VPN tunnel", metadata: [
                    "error": "\(error)",
                    "error_type": "\(type(of: error))",
                    "localized_description": error.localizedDescription
                ])
                await cleanupOnError()
                completionHandler(error)
            }
        }
    }
    
    /**
     * NAME: stopTunnel
     *
     * DESCRIPTION:
     *     Stops the VPN tunnel and cleans up resources.
     *     Disconnects SDWAN connection and restores network configuration.
     *
     * PARAMETERS:
     *     reason - Reason for tunnel stop
     *     completionHandler - Completion callback
     */
    public override func stopTunnel(with reason: NEProviderStopReason,
                                  completionHandler: @escaping () -> Void) {
        logger.info("Stopping VPN tunnel", metadata: ["reason": "\(reason.rawValue)"])

        Task {
            await cleanupTunnel(reason: reason)
            completionHandler()
        }
    }

    // MARK: - Sleep/Wake Lifecycle Management

    /**
     * NAME: sleep
     *
     * DESCRIPTION:
     *     Handles device entering sleep mode.
     *     Optimizes VPN connection for background operation while maintaining connectivity.
     *     Called by iOS when device is about to sleep.
     *
     * PARAMETERS:
     *     completionHandler - Completion callback to signal sleep preparation is complete
     */
    public override func sleep(completionHandler: @escaping () -> Void) {
        // NSLog("Device entering sleep mode - optimizing for background operation") // Debug NSLog commented for production
        logger.info("VPN Extension entering sleep mode")

        Task {
            // Notify connection manager about sleep state
            await connectionManager?.handleDeviceSleep()

            // Signal that sleep preparation is complete
            completionHandler()

            logger.info("VPN Extension sleep preparation completed")
        }
    }

    /**
     * NAME: wake
     *
     * DESCRIPTION:
     *     Handles device waking up from sleep mode.
     *     Restores full VPN operation and verifies connection integrity.
     *     Called by iOS when device wakes up.
     */
    public override func wake() {
        // NSLog("Device waking up - restoring full VPN operation") // Debug NSLog commented for production
        logger.info("VPN Extension waking up from sleep")

        Task {
            // Notify connection manager about wake state
            await connectionManager?.handleDeviceWake()

            logger.info("VPN Extension wake restoration completed")
        }
    }

    /**
     * NAME: handleAppMessage
     *
     * DESCRIPTION:
     *     Handles messages from the main app (server switching, configuration updates).
     *     Supports runtime configuration changes without tunnel restart.
     *
     * PARAMETERS:
     *     messageData - Message data from app
     *     completionHandler - Response callback
     */
    public override func handleAppMessage(_ messageData: Data, 
                                        completionHandler: ((Data?) -> Void)?) {
        // logger.debug("Received app message", metadata: ["size": "\(messageData.count)"]) // Debug log commented for production
        
        Task {
            do {
                let response = try await processAppMessage(messageData)
                completionHandler?(response)
            } catch {
                logger.error("Failed to process app message", metadata: ["error": "\(error)"])
                completionHandler?(nil)
            }
        }
    }
    
    // MARK: - Reconnection Handling

    /**
     * NAME: handleReconnectRequest
     *
     * DESCRIPTION:
     *     Handles reconnection requests directly within NetworkExtension.
     *     Performs disconnect+connect cycle using the same logic as VPNService.
     *
     * PARAMETERS:
     *     reason - Reason for reconnection request
     */
    private func handleReconnectRequest(reason: ReconnectReason) async {
        logger.info("NetworkExtension handling reconnection request", metadata: [
            "reason": reason.rawValue,
            "tunnel_started": "\(isStarted)",
            "is_reconnecting": "\(isReconnecting)"
        ])

        guard isStarted, let connectionManager = connectionManager, let config = configuration else {
            logger.warning("Cannot reconnect - tunnel not properly initialized")
            return
        }

        // Prevent concurrent reconnections
        guard !isReconnecting else {
            logger.info("Reconnection already in progress, ignoring request", metadata: [
                "reason": reason.rawValue
            ])
            return
        }

        // Set reconnecting flag
        isReconnecting = true

        do {
            logger.info("Starting NetworkExtension reconnection process")

            // Step 1: Disconnect current connection
            logger.info("Step 1: Disconnecting current connection")
            try await connectionManager.disconnect()

            // Step 2: Wait briefly for cleanup
            try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

            // Step 3: Reconnect to the same server
            logger.info("Step 2: Reconnecting to server", metadata: [
                "server": "\(config.serverAddress):\(config.serverPort)"
            ])

            // Create server info for reconnection
            let serverInfo = ServerInfo(
                id: "tunnel-server",
                name: "Tunnel Server",
                nameEn: "Tunnel Server",
                serverName: config.serverAddress,
                serverPort: config.serverPort,
                isAuto: false,
                ping: 0,
                status: .online
            )

            // Reconnect (this includes authentication and starts packet processing)
            _ = try await connectionManager.connect(to: serverInfo)

            logger.info("NetworkExtension reconnection completed successfully")

        } catch {
            logger.error("NetworkExtension reconnection failed", metadata: [
                "reason": reason.rawValue,
                "error": error.localizedDescription
            ])

            // If reconnection fails, we should probably stop the tunnel
            // to avoid being stuck in a broken state
            logger.warning("Stopping tunnel due to reconnection failure")
            await cleanupTunnel(reason: .connectionFailed)
        }

        // Reset reconnecting flag
        isReconnecting = false
    }

    // MARK: - Component Initialization
    
    /**
     * NAME: initializeComponents
     *
     * DESCRIPTION:
     *     Initializes core VPN components with configuration.
     *     Simplified to only setup ConnectionManager - NetworkExtension handles routing/DNS directly.
     *
     * PARAMETERS:
     *     config - Tunnel configuration
     *
     * THROWS:
     *     PlatformError - If component initialization fails
     */
    private func initializeComponents(config: VPNTunnelConfiguration) async throws {
        logger.info("Initializing VPN components (simplified architecture)")

        // Initialize ServerManager for ConnectionManager
        let serverManager = ServerManager(logger: logger)
        self.serverManager = serverManager

        // Initialize ConnectionManager with SDWAN protocol
        let connectionConfig = ConnectionConfiguration(
            serverAddress: config.serverAddress,
            serverPort: config.serverPort,
            username: config.username,
            password: config.password,
            mtu: config.mtu,
            encryption: EncryptionMethod(rawValue: config.encryptionMethod) ?? .none,
            timeout: 10.0,
            retryCount: 3,
            retryInterval: 1.0,
            heartbeatInterval: 15.0  // 统一使用15秒心跳间隔
        )

        connectionManager = ConnectionManager(
            configuration: connectionConfig,
            packetFlow: self.packetFlow,
            serverManager: serverManager,
            logger: logger
        )

        // Darwin Notifications已在init()中设置，无需重复设置

        // Reconnection is now handled internally by ConnectionManager
        logger.info("VPN components initialized successfully (simplified)", metadata: [
            "internal_reconnection_enabled": "true"
        ])
    }

    /**
     * NAME: startSDWANConnection
     *
     * DESCRIPTION:
     *     Establishes SDWAN connection with authentication and data processing.
     *     This combines connection, authentication, and packet processing in one step.
     *
     * PARAMETERS:
     *     config - Tunnel configuration
     *
     * RETURNS:
     *     [String: Any] - Server configuration from OpenAck response
     *
     * THROWS:
     *     PlatformError.connectionFailed - If connection or authentication fails
     */
    private func startSDWANConnection(config: VPNTunnelConfiguration) async throws -> [String: Any] {
        guard let connectionManager = connectionManager else {
            logger.error("ConnectionManager not initialized")
            throw PlatformError.tunnelNotStarted
        }

        logger.info("Starting SDWAN authentication", metadata: [
            "server": "\(config.serverAddress):\(config.serverPort)",
            "username": config.username,
            "encryption_method": "\(config.encryptionMethod)"
        ])

        // Create server info for authentication
        let serverInfo = ServerInfo(
            id: "tunnel-server",
            name: "Tunnel Server",
            nameEn: "Tunnel Server",
            serverName: config.serverAddress,
            serverPort: config.serverPort,
            isAuto: false,
            ping: 0,
            status: .online
        )

        // NSLog("Step 3a: Starting ConnectionManager for authentication") // Debug NSLog commented for production
        logger.info("Step 3a: Starting ConnectionManager for authentication")
        // Start ConnectionManager first
        try await connectionManager.start()
        // NSLog("Step 3a completed: ConnectionManager started") // Debug NSLog commented for production
        logger.info("Step 3a completed: ConnectionManager started")

        // NSLog("Step 3b: Authenticating with server \(serverInfo.serverName):\(serverInfo.serverPort)") // Debug NSLog commented for production
        logger.info("Step 3b: Authenticating with server")

        // Connect to server (this includes authentication and starts packet processing)
        let serverConfig = try await connectionManager.connect(to: serverInfo)

        // NSLog("Step 3b completed: Connection and authentication successful") // Debug NSLog commented for production
        logger.info("Step 3b completed: Connection and authentication successful")

        // Return network configuration from OpenAck response
        // NSLog("Received server configuration with \(serverConfig.count) parameters") // Debug NSLog commented for production
        logger.info("Received server configuration from OpenAck", metadata: [
            "config_count": "\(serverConfig.count)"
        ])

        return serverConfig
    }




    
    // MARK: - Network Configuration
    
    /**
     * NAME: createNetworkSettings
     *
     * DESCRIPTION:
     *     Creates NetworkExtension network settings for tunnel configuration.
     *     Configures IP addresses, routes, DNS, and MTU based on SDWAN requirements.
     *     Uses server-provided configuration from OpenAck response when available.
     *
     * PARAMETERS:
     *     config - Tunnel configuration
     *     serverConfig - Optional server configuration from OpenAck response
     *
     * RETURNS:
     *     NEPacketTunnelNetworkSettings - Configured network settings
     *
     * THROWS:
     *     PlatformError.networkConfigurationFailed - If settings creation fails
     */
    private func createNetworkSettings(for config: VPNTunnelConfiguration, serverConfig: [String: Any]? = nil) throws -> NEPacketTunnelNetworkSettings {
        // print("createNetworkSettings called") // Debug print commented for production
        // print("serverConfig: \(serverConfig ?? [:])") // Debug print commented for production
        // print("config.tunnelIP: \(config.tunnelIP)") // Debug print commented for production

        // Use server-provided configuration when available, fallback to static config
        let tunnelIP = (serverConfig?["ip"] as? String) ?? config.tunnelIP
        let mtu = (serverConfig?["mtu"] as? String).flatMap { Int($0) } ?? config.mtu

        // print("Final tunnelIP: \(tunnelIP) (from \(serverConfig?["ip"] != nil ? "server" : "config"))") // Debug print commented for production
        // Extract DNS servers from server config
        var dnsServers = config.dnsServers
        if let serverConfig = serverConfig {
            var serverDNS: [String] = []
            if let dns1 = serverConfig["dns"] as? String {
                serverDNS.append(dns1)
            }
            if let dns2 = serverConfig["dns2"] as? String {
                serverDNS.append(dns2)
            }
            if !serverDNS.isEmpty {
                dnsServers = serverDNS
            }
        }

        // Use /32 mask for tunnel IP as recommended
        let netmask = "***************"

        logger.info("Creating network settings", metadata: [
            "server": config.serverAddress,
            "tunnel_ip": tunnelIP,
            "mtu": "\(mtu)",
            "dns_servers": "\(dnsServers.count)",
            "source": serverConfig != nil ? "server_config" : "static_config"
        ])

        // NSLog("Network configuration:") // Debug NSLog commented for production
        // NSLog("- Tunnel IP: \(tunnelIP) (from \(serverConfig?["ip"] != nil ? "server OpenAck" : "static config"))") // Debug NSLog commented for production
        // NSLog("- MTU: \(mtu) (from \(serverConfig?["mtu"] != nil ? "server OpenAck" : "static config"))") // Debug NSLog commented for production
        // NSLog("- DNS: \(dnsServers) (from \(serverConfig != nil ? "server OpenAck" : "static config"))") // Debug NSLog commented for production
        // NSLog("- Netmask: \(netmask) (/32 for point-to-point)") // Debug NSLog commented for production
        // if let serverConfig = serverConfig {
        //     NSLog("- Server config received: \(serverConfig)") // Debug NSLog commented for production
        // } else {
        //     NSLog("- No server config received, using static values") // Debug NSLog commented for production
        // }

        // Resolve server address to IP for tunnelRemoteAddress
        // iOS requires tunnelRemoteAddress to be a valid IP address, not hostname
        let resolvedServerIP = resolveServerAddressToIP(config.serverAddress)

        // Create base network settings with resolved server IP
        let settings = NEPacketTunnelNetworkSettings(tunnelRemoteAddress: resolvedServerIP)

        // Configure IPv4 settings with server-provided or static tunnel IP
        let ipv4Settings = NEIPv4Settings(
            addresses: [tunnelIP],
            subnetMasks: [netmask]
        )
        
        // Configure routing based on routing mode
        if config.routingMode == "custom" && !config.customRoutes.isEmpty {
            // Custom routing mode - only route specified networks through VPN
            var customRoutes: [NEIPv4Route] = []

            // NSLog("Using custom routing mode with \(config.customRoutes.count) routes") // Debug NSLog commented for production

            for routeString in config.customRoutes {
                let trimmedRoute = routeString.trimmingCharacters(in: .whitespaces)
                if trimmedRoute.isEmpty { continue }

                // Parse CIDR notation (e.g., "***********/24")
                let components = trimmedRoute.split(separator: "/")
                if components.count == 2,
                   let ipString = components.first,
                   let prefixString = components.last,
                   let prefixLength = Int(prefixString),
                   prefixLength >= 0 && prefixLength <= 32 {

                    // Create subnet mask from prefix length
                    let mask = (0xFFFFFFFF << (32 - prefixLength)) & 0xFFFFFFFF
                    let subnetMask = String(format: "%d.%d.%d.%d",
                                           (mask >> 24) & 0xFF,
                                           (mask >> 16) & 0xFF,
                                           (mask >> 8) & 0xFF,
                                           mask & 0xFF)

                    let route = NEIPv4Route(destinationAddress: String(ipString), subnetMask: subnetMask)
                    customRoutes.append(route)
                    // NSLog("Added custom route: \(trimmedRoute)") // Debug NSLog commented for production
                } else {
                    // NSLog("Invalid CIDR format: \(trimmedRoute)") // Debug NSLog commented for production
                }
            }

            // Use custom routes if any are valid, otherwise fall back to default
            ipv4Settings.includedRoutes = customRoutes.isEmpty ? [NEIPv4Route.default()] : customRoutes
            // NSLog("Applied \(customRoutes.count) custom routes") // Debug NSLog commented for production
        } else {
            // All traffic routing mode (default)
            ipv4Settings.includedRoutes = [NEIPv4Route.default()]
            // NSLog("Using all-traffic routing (default route)") // Debug NSLog commented for production
        }
        
        // Configure excluded routes for server accessibility and local networks
        var excludedRoutes: [NEIPv4Route] = []

        // NSLog("Configuring excluded routes") // Debug NSLog commented for production
        // NSLog("- Server excludedIPs from config: \(config.excludedIPs)") // Debug NSLog commented for production

        // Exclude server IPs (provided by app layer after resolution)
        for excludedIP in config.excludedIPs {
            // NSLog("- Processing excluded IP: \(excludedIP)") // Debug NSLog commented for production
            if let route = createExcludedRoute(for: excludedIP) {
                excludedRoutes.append(route)
                // NSLog("- Added excluded route for: \(excludedIP)") // Debug NSLog commented for production
            } else {
                // NSLog("- ERROR: Failed to create route for: \(excludedIP)") // Debug NSLog commented for production
            }
        }

        // Exclude local networks (equivalent to Go backend local network protection)
        let localExclusions = createLocalNetworkExclusions()
        excludedRoutes.append(contentsOf: localExclusions)
        // NSLog("- Added \(localExclusions.count) local network exclusions") // Debug NSLog commented for production
        
        ipv4Settings.excludedRoutes = excludedRoutes
        settings.ipv4Settings = ipv4Settings
        
        // Configure DNS settings using server-provided or static DNS
        let dnsSettings = NEDNSSettings(servers: dnsServers)
        settings.dnsSettings = dnsSettings

        // Set MTU using server-provided or static MTU
        settings.mtu = NSNumber(value: mtu)
        
        // NSLog("Final network configuration:") // Debug NSLog commented for production
        // NSLog("- Tunnel IP: \(tunnelIP)") // Debug NSLog commented for production
        // NSLog("- MTU: \(mtu)") // Debug NSLog commented for production
        // NSLog("- DNS servers: \(dnsServers)") // Debug NSLog commented for production
        // NSLog("- Total excluded routes: \(excludedRoutes.count)") // Debug NSLog commented for production
        // for (index, route) in excludedRoutes.enumerated() {
        //     NSLog("- Excluded route \(index + 1): \(route.destinationAddress)/\(route.destinationSubnetMask)") // Debug NSLog commented for production
        // }

        // Store tunnel IP in App Group for main app access
        storeTunnelIPInAppGroup(tunnelIP)

        logger.info("Network settings created successfully", metadata: [
            "included_routes": "1", // Default route
            "excluded_routes": "\(excludedRoutes.count)",
            "dns_servers": "\(config.dnsServers.count)",
            "tunnel_ip_stored": tunnelIP
        ])

        return settings
    }

    /**
     * NAME: createExcludedRoute
     *
     * DESCRIPTION:
     *     Creates excluded route for specific IP address or hostname.
     *     Ensures server and critical IPs bypass VPN tunnel.
     *     Attempts to resolve hostname to IP if needed.
     *
     * PARAMETERS:
     *     ipAddress - IP address or hostname to exclude
     *
     * RETURNS:
     *     NEIPv4Route? - Excluded route or nil if invalid
     */
    private func createExcludedRoute(for ipAddress: String) -> NEIPv4Route? {
        // First check if it's already a valid IP address
        if let _ = IPv4Address(ipAddress) {
            return NEIPv4Route(destinationAddress: ipAddress, subnetMask: "***************")
        }

        // If not an IP, try to get cached IP from App Group
        if let cachedIP = getCachedServerIPFromAppGroup(ipAddress) {
            logger.info("Using cached IP for exclusion", metadata: [
                "hostname": ipAddress,
                "cached_ip": cachedIP
            ])
            return NEIPv4Route(destinationAddress: cachedIP, subnetMask: "***************")
        }

        // If no cached IP, try synchronous DNS resolution as last resort
        if let resolvedIP = performSyncDNSResolution(ipAddress) {
            logger.info("Resolved hostname for exclusion", metadata: [
                "hostname": ipAddress,
                "resolved_ip": resolvedIP
            ])
            return NEIPv4Route(destinationAddress: resolvedIP, subnetMask: "***************")
        }

        logger.warning("Failed to resolve hostname for exclusion", metadata: [
            "hostname": ipAddress,
            "note": "Server may be routed through VPN tunnel"
        ])
        return nil
    }

    /**
     * NAME: createIncludedRoutes
     *
     * DESCRIPTION:
     *     Creates included routes based on routing configuration.
     *     Supports both all-traffic and custom routing modes.
     *
     * PARAMETERS:
     *     config - VPN tunnel configuration containing routing settings
     *
     * RETURNS:
     *     [NEIPv4Route] - Array of routes to include in VPN tunnel
     */
    private func createIncludedRoutes(config: VPNTunnelConfiguration) -> [NEIPv4Route] {
        // NSLog("Creating included routes - mode: \(config.routingMode)") // Debug NSLog commented for production

        switch config.routingMode {
        case "custom":
            return createCustomIncludedRoutes(customRoutes: config.customRoutes)
        case "all":
            fallthrough
        default:
            // Default to all traffic routing
            // NSLog("Using all-traffic routing (default route)") // Debug NSLog commented for production
            return [NEIPv4Route.default()]
        }
    }

    /**
     * NAME: createCustomIncludedRoutes
     *
     * DESCRIPTION:
     *     Creates included routes for custom routing mode.
     *     Parses CIDR notation routes and creates NEIPv4Route objects.
     *
     * PARAMETERS:
     *     customRoutes - Array of custom routes in CIDR notation
     *
     * RETURNS:
     *     [NEIPv4Route] - Array of custom routes
     */
    private func createCustomIncludedRoutes(customRoutes: [String]) -> [NEIPv4Route] {
        var routes: [NEIPv4Route] = []

        // NSLog("Processing \(customRoutes.count) custom routes") // Debug NSLog commented for production

        for routeString in customRoutes {
            let trimmedRoute = routeString.trimmingCharacters(in: .whitespaces)
            if trimmedRoute.isEmpty {
                continue
            }

            // NSLog("Processing custom route: \(trimmedRoute)") // Debug NSLog commented for production

            if let route = parseCustomRoute(routeString: trimmedRoute) {
                routes.append(route)
                // NSLog("Added custom route: \(trimmedRoute)") // Debug NSLog commented for production
            } else {
                // NSLog("ERROR: Failed to parse custom route: \(trimmedRoute)") // Debug NSLog commented for production
            }
        }

        // If no valid custom routes, fall back to default route
        if routes.isEmpty {
            // NSLog("No valid custom routes, falling back to default route") // Debug NSLog commented for production
            routes.append(NEIPv4Route.default())
        }

        return routes
    }

    /**
     * NAME: parseCustomRoute
     *
     * DESCRIPTION:
     *     Parses a custom route string in CIDR notation and creates NEIPv4Route.
     *
     * PARAMETERS:
     *     routeString - Route in CIDR notation (e.g., "***********/24")
     *
     * RETURNS:
     *     NEIPv4Route? - Parsed route or nil if invalid
     */
    private func parseCustomRoute(routeString: String) -> NEIPv4Route? {
        let components = routeString.split(separator: "/")
        guard components.count == 2,
              let ipString = components.first,
              let prefixString = components.last,
              let prefixLength = Int(prefixString),
              prefixLength >= 0 && prefixLength <= 32 else {
            // NSLog("Invalid CIDR format: \(routeString)") // Debug NSLog commented for production
            return nil
        }

        // Create subnet mask from prefix length
        let mask = (0xFFFFFFFF << (32 - prefixLength)) & 0xFFFFFFFF
        let subnetMask = String(format: "%d.%d.%d.%d",
                               (mask >> 24) & 0xFF,
                               (mask >> 16) & 0xFF,
                               (mask >> 8) & 0xFF,
                               mask & 0xFF)

        return NEIPv4Route(destinationAddress: String(ipString), subnetMask: subnetMask)
    }

    /**
     * NAME: createLocalNetworkExclusions
     *
     * DESCRIPTION:
     *     Creates excluded routes for local networks.
     *     Equivalent to Go backend's local network protection.
     *
     * RETURNS:
     *     [NEIPv4Route] - Array of local network exclusions
     */
    private func createLocalNetworkExclusions() -> [NEIPv4Route] {
        return [
            // Link-local addresses (RFC 3927)
            NEIPv4Route(destinationAddress: "***********", subnetMask: "***********"),

            // Multicast addresses (RFC 3171)
            NEIPv4Route(destinationAddress: "*********", subnetMask: "240.0.0.0")
        ]
    }

    // MARK: - Packet Processing

    /**
     * NOTE: Packet Processing Architecture
     *
     * DESCRIPTION:
     *     In iOS/macOS NetworkExtension architecture, packet processing is handled automatically
     *     by ConnectionManager's internal packet sender/receiver loops. Unlike Go backend where
     *     we manually read from TUN device, NetworkExtension provides packetFlow which is
     *     automatically processed by ConnectionManager.runPacketSender() and runPacketReceiver().
     *
     *     Flow:
     *     1. System → NEPacketTunnelFlow → ConnectionManager.runPacketSender() → SDWAN Server
     *     2. SDWAN Server → ConnectionManager.runPacketReceiver() → NEPacketTunnelFlow → System
     *
     *     This eliminates the need for manual packet processing in PacketTunnelProvider.
     */

    // MARK: - Configuration Management

    /**
     * NAME: parseConfiguration
     *
     * DESCRIPTION:
     *     Parses tunnel configuration from NetworkExtension options.
     *     Extracts server details, credentials, and network parameters.
     *
     * PARAMETERS:
     *     options - Configuration options from system/app
     *
     * RETURNS:
     *     TunnelConfiguration - Parsed configuration
     *
     * THROWS:
     *     PlatformError.configurationInvalid - If required parameters missing
     */
    private func parseConfiguration(from options: [String: NSObject]?) throws -> VPNTunnelConfiguration {
        guard let options = options else {
            throw PlatformError.configurationInvalid
        }

        // Extract required parameters
        guard let serverAddress = options["serverAddress"] as? String,
              let serverPort = options["serverPort"] as? NSNumber,
              let username = options["username"] as? String,
              let password = options["password"] as? String else {
            throw PlatformError.configurationInvalid
        }

        // Extract optional parameters with defaults
        let mtu = (options["mtu"] as? NSNumber)?.intValue ?? 1400
        let encryptionMethod = (options["encryptionMethod"] as? NSNumber)?.uint8Value ?? 1  // Default to XOR (0x01) to match Windows
        let dnsServers = (options["dnsServers"] as? [String]) ?? ["*******", "*******"]
        let excludedIPs = (options["excludedIPs"] as? [String]) ?? []
        let tunnelIP = (options["tunnelIP"] as? String) ?? "********"
        let routingMode = (options["routingMode"] as? String) ?? "all"
        let customRoutes = (options["customRoutes"] as? [String]) ?? []

        // NSLog("Parsed routing configuration: mode=\(routingMode), routes=\(customRoutes)") // Debug NSLog commented for production

        return VPNTunnelConfiguration(
            serverAddress: serverAddress,
            serverPort: serverPort.intValue,
            username: username,
            password: password,
            mtu: mtu,
            encryptionMethod: encryptionMethod,
            dnsServers: dnsServers,
            excludedIPs: excludedIPs,
            tunnelIP: tunnelIP,
            routingMode: routingMode,
            customRoutes: customRoutes
        )
    }

    /**
     * NAME: processAppMessage
     *
     * DESCRIPTION:
     *     Processes messages from main app for runtime configuration changes.
     *     Supports server switching and configuration updates without tunnel restart.
     *
     * PARAMETERS:
     *     messageData - Message data from app
     *
     * RETURNS:
     *     Data? - Response data
     *
     * THROWS:
     *     PlatformError - If message processing fails
     */
    private func processAppMessage(_ messageData: Data) async throws -> Data? {
        // Parse message (simplified JSON format)
        guard let message = try? JSONSerialization.jsonObject(with: messageData) as? [String: Any],
              let action = message["action"] as? String else {
            throw PlatformError.configurationInvalid
        }

        // logger.debug("Processing app message", metadata: ["action": action]) // Debug log commented for production

        switch action {
        case "switch_server":
            return try await handleServerSwitch(message)
        case "update_config":
            return try await handleConfigUpdate(message)
        case "get_status":
            return try await handleStatusRequest()
        default:
            logger.warning("Unknown app message action", metadata: ["action": action])
            return nil
        }
    }

    /**
     * NAME: handleServerSwitch
     *
     * DESCRIPTION:
     *     Handles server switching request from app.
     *     Updates connection to new server without full tunnel restart.
     *
     * PARAMETERS:
     *     message - Server switch message data
     *
     * RETURNS:
     *     Data? - Response data
     */
    private func handleServerSwitch(_ message: [String: Any]) async throws -> Data? {
        guard let serverAddress = message["serverAddress"] as? String,
              let serverPort = message["serverPort"] as? NSNumber else {
            throw PlatformError.configurationInvalid
        }

        logger.info("Switching to new server", metadata: [
            "server": "\(serverAddress):\(serverPort.intValue)"
        ])

        // Note: In iOS/macOS, server switching requires NetworkExtension rebuild
        // This is different from Go backend which can switch UDP connections
        let response: [String: Any] = [
            "success": true,
            "message": "Server switch requires tunnel restart",
            "requires_restart": true
        ]

        return try JSONSerialization.data(withJSONObject: response)
    }

    /**
     * NAME: handleConfigUpdate
     *
     * DESCRIPTION:
     *     Handles configuration update request from app.
     *
     * PARAMETERS:
     *     message - Configuration update message
     *
     * RETURNS:
     *     Data? - Response data
     */
    private func handleConfigUpdate(_ message: [String: Any]) async throws -> Data? {
        logger.info("Updating configuration")

        // Update supported runtime configurations
        if let newDNS = message["dnsServers"] as? [String] {
            try await updateDNSConfiguration(newDNS)
        }

        if let newMTU = message["mtu"] as? NSNumber {
            try await updateMTUConfiguration(newMTU.intValue)
        }

        let response: [String: Any] = [
            "success": true,
            "message": "Configuration updated successfully"
        ]

        return try JSONSerialization.data(withJSONObject: response)
    }

    /**
     * NAME: handleStatusRequest
     *
     * DESCRIPTION:
     *     Handles status request from app.
     *
     * RETURNS:
     *     Data? - Status response data
     */
    private func handleStatusRequest() async throws -> Data? {
        let connectionState = await connectionManager?.getConnectionState() ?? .disconnected

        let status: [String: Any] = [
            "connected": isStarted,
            "state": connectionState.rawValue,
            "upload_speed": 0,
            "download_speed": 0,
            "total_upload": 0,
            "total_download": 0
        ]

        return try JSONSerialization.data(withJSONObject: status)
    }

    // MARK: - Runtime Configuration Updates

    /**
     * NAME: updateDNSConfiguration
     *
     * DESCRIPTION:
     *     Updates DNS configuration during runtime.
     *
     * PARAMETERS:
     *     dnsServers - New DNS servers
     */
    private func updateDNSConfiguration(_ dnsServers: [String]) async throws {
        guard let config = configuration else {
            throw PlatformError.configurationInvalid
        }

        // Update configuration
        var updatedConfig = config
        updatedConfig.dnsServers = dnsServers
        self.configuration = updatedConfig

        // Create updated network settings
        let networkSettings = try createNetworkSettings(for: updatedConfig)
        try await setTunnelNetworkSettings(networkSettings)

        logger.info("DNS configuration updated", metadata: ["servers": "\(dnsServers.count)"])
    }

    /**
     * NAME: updateMTUConfiguration
     *
     * DESCRIPTION:
     *     Updates MTU configuration during runtime.
     *     Note: MTU changes require tunnel restart in NetworkExtension.
     *
     * PARAMETERS:
     *     mtu - New MTU value
     */
    private func updateMTUConfiguration(_ mtu: Int) async throws {
        guard let config = configuration else {
            throw PlatformError.configurationInvalid
        }

        // Update configuration
        var updatedConfig = config
        updatedConfig.mtu = mtu
        self.configuration = updatedConfig

        // Note: MTU changes in NetworkExtension require tunnel restart
        // The new MTU will be applied on next tunnel start
        logger.info("MTU configuration updated (requires tunnel restart)", metadata: ["mtu": "\(mtu)"])
    }

    // MARK: - Cleanup and Error Handling

    /**
     * NAME: cleanupTunnel
     *
     * DESCRIPTION:
     *     Cleans up tunnel resources and disconnects SDWAN connection.
     *     Simplified cleanup for streamlined architecture.
     *
     * PARAMETERS:
     *     reason - Reason for tunnel cleanup
     */
    private func cleanupTunnel(reason: NEProviderStopReason) async {
        logger.info("Cleaning up tunnel (simplified)", metadata: ["reason": "\(reason.rawValue)"])

        isStarted = false

        // Stop and disconnect SDWAN connection
        if let connectionManager = connectionManager {
            try? await connectionManager.disconnect()
            await connectionManager.stop()
        }

        // Clear tunnel IP from App Group
        clearTunnelIPFromAppGroup()

        // Clean up Darwin Notifications
        cleanupDarwinNotifications()

        // Clean up components
        connectionManager = nil
        configuration = nil
        serverManager = nil

        logger.info("Tunnel cleanup completed (simplified)")
    }

    /**
     * NAME: cleanupOnError
     *
     * DESCRIPTION:
     *     Cleans up resources when tunnel start fails.
     *     Simplified cleanup for streamlined architecture.
     */
    private func cleanupOnError() async {
        logger.warning("Cleaning up due to error (simplified)")

        isStarted = false

        // Clean up partially initialized components
        if let connectionManager = connectionManager {
            try? await connectionManager.disconnect()
            await connectionManager.stop()
        }

        // Reset state
        connectionManager = nil
        configuration = nil
    }

    // MARK: - App Group Data Sharing

    /**
     * NAME: storeTunnelIPInAppGroup
     *
     * DESCRIPTION:
     *     Stores the current tunnel IP address in App Group shared storage
     *     for access by the main application.
     *
     * PARAMETERS:
     *     tunnelIP - The tunnel IP address to store
     */
    private func storeTunnelIPInAppGroup(_ tunnelIP: String) {
        // print("storeTunnelIPInAppGroup called with tunnelIP: \(tunnelIP)") // Debug print commented for production

        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.error("Failed to access App Group UserDefaults")
            // NSLog("Failed to access App Group UserDefaults") // Debug NSLog commented for production
            // print("ERROR: Failed to access App Group UserDefaults") // Debug print commented for production
            return
        }

        let tunnelInfo: [String: Any] = [
            "tunnel_ip": tunnelIP,
            "timestamp": Date().timeIntervalSince1970,
            "server_address": configuration?.serverAddress ?? "",
            "mtu": configuration?.mtu ?? 1400
        ]

        // print("About to store tunnel info: \(tunnelInfo)") // Debug print commented for production

        userDefaults.set(tunnelInfo, forKey: "vpn_tunnel_info")
        userDefaults.synchronize()

        // Verify storage
        // if let storedData = userDefaults.object(forKey: "vpn_tunnel_info") {
        //     print("Successfully stored and verified tunnel info: \(storedData)") // Debug print commented for production
        // } else {
        //     print("Failed to verify stored tunnel info") // Debug print commented for production
        // }

        logger.info("Stored tunnel IP in App Group", metadata: [
            "tunnel_ip": tunnelIP,
            "server": configuration?.serverAddress ?? ""
        ])
        // NSLog("Stored tunnel IP in App Group: \(tunnelIP)") // Debug NSLog commented for production
    }

    /**
     * NAME: clearTunnelIPFromAppGroup
     *
     * DESCRIPTION:
     *     Clears tunnel IP information from App Group when tunnel stops.
     */
    private func clearTunnelIPFromAppGroup() {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.error("Failed to access App Group UserDefaults for cleanup")
            return
        }

        userDefaults.removeObject(forKey: "vpn_tunnel_info")
        userDefaults.synchronize()

        logger.info("Cleared tunnel IP from App Group")
        // NSLog("Cleared tunnel IP from App Group") // Debug NSLog commented for production
    }

    /**
     * NAME: resolveServerAddressToIP
     *
     * DESCRIPTION:
     *     Resolves server address to IP address for tunnelRemoteAddress.
     *     First tries to get cached IP from App Group (shared with main app ServerManager),
     *     then performs synchronous DNS resolution if needed.
     *     iOS requires tunnelRemoteAddress to be a valid IP address.
     *
     * PARAMETERS:
     *     serverAddress - Server hostname or IP address to resolve
     *
     * RETURNS:
     *     String - Resolved IP address or original address if already IP
     */
    private func resolveServerAddressToIP(_ serverAddress: String) -> String {
        // Check if serverAddress is already an IP address
        if isValidIPAddress(serverAddress) {
            logger.info("Server address is already an IP", metadata: ["ip": serverAddress])
            return serverAddress
        }

        // Try to get cached IP from App Group (shared with main app ServerManager)
        if let cachedIP = getCachedServerIPFromAppGroup(serverAddress) {
            logger.info("Using cached server IP from App Group", metadata: [
                "hostname": serverAddress,
                "cached_ip": cachedIP
            ])
            return cachedIP
        }

        // Perform synchronous DNS resolution as fallback
        logger.info("Performing DNS resolution for server address", metadata: ["hostname": serverAddress])

        if let resolvedIP = performSyncDNSResolution(serverAddress) {
            logger.info("Successfully resolved server IP", metadata: [
                "hostname": serverAddress,
                "resolved_ip": resolvedIP
            ])

            // Cache the resolved IP in App Group for future use
            storeCachedServerIPInAppGroup(serverAddress, resolvedIP)

            return resolvedIP
        } else {
            logger.error("Failed to resolve server IP, using hostname as fallback", metadata: [
                "hostname": serverAddress
            ])

            // Return hostname as last resort - this may cause tunnel startup failure
            // but it's better than crashing
            return serverAddress
        }
    }

    /**
     * NAME: isValidIPAddress
     *
     * DESCRIPTION:
     *     Checks if a string is a valid IPv4 address.
     *
     * PARAMETERS:
     *     address - String to check
     *
     * RETURNS:
     *     Bool - true if valid IPv4 address, false otherwise
     */
    private func isValidIPAddress(_ address: String) -> Bool {
        var sin = sockaddr_in()
        return address.withCString { cstring in
            inet_pton(AF_INET, cstring, &sin.sin_addr) == 1
        }
    }

    /**
     * NAME: getCachedServerIPFromAppGroup
     *
     * DESCRIPTION:
     *     Gets cached server IP from App Group shared storage.
     *     This allows VPN extension to use IPs cached by main app's ServerManager.
     *
     * PARAMETERS:
     *     hostname - Server hostname to lookup
     *
     * RETURNS:
     *     String? - Cached IP address or nil if not found
     */
    private func getCachedServerIPFromAppGroup(_ hostname: String) -> String? {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.warning("Failed to access App Group UserDefaults for server IP cache")
            return nil
        }

        guard let serverIPCache = userDefaults.object(forKey: "server_ip_cache") as? [String: Any] else {
            return nil
        }

        guard let cachedIP = serverIPCache[hostname] as? String else {
            return nil
        }

        // Check if cached data is recent (within last 30 minutes)
        if let cacheData = serverIPCache["\(hostname)_metadata"] as? [String: Any],
           let timestamp = cacheData["timestamp"] as? TimeInterval {
            let age = Date().timeIntervalSince1970 - timestamp
            if age > 1800 { // 30 minutes
                logger.warning("Cached server IP is stale", metadata: [
                    "hostname": hostname,
                    "age_minutes": String(format: "%.1f", age / 60.0)
                ])
                return nil
            }
        }

        return cachedIP
    }

    /**
     * NAME: storeCachedServerIPInAppGroup
     *
     * DESCRIPTION:
     *     Stores resolved server IP in App Group for sharing with main app.
     *
     * PARAMETERS:
     *     hostname - Server hostname
     *     ip - Resolved IP address
     */
    private func storeCachedServerIPInAppGroup(_ hostname: String, _ ip: String) {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.warning("Failed to access App Group UserDefaults for storing server IP cache")
            return
        }

        var serverIPCache = userDefaults.object(forKey: "server_ip_cache") as? [String: Any] ?? [:]

        // Store IP and metadata
        serverIPCache[hostname] = ip
        serverIPCache["\(hostname)_metadata"] = [
            "timestamp": Date().timeIntervalSince1970,
            "source": "vpn_extension"
        ]

        userDefaults.set(serverIPCache, forKey: "server_ip_cache")
        userDefaults.synchronize()

        logger.info("Stored server IP in App Group cache", metadata: [
            "hostname": hostname,
            "ip": ip
        ])
    }

    /**
     * NAME: performSyncDNSResolution
     *
     * DESCRIPTION:
     *     Performs synchronous DNS resolution for hostname to get IPv4 address.
     *     Uses CFHost API for immediate resolution.
     *
     * PARAMETERS:
     *     hostname - Hostname to resolve
     *
     * RETURNS:
     *     String? - Resolved IPv4 address or nil if resolution fails
     */
    private func performSyncDNSResolution(_ hostname: String) -> String? {
        let host = CFHostCreateWithName(nil, hostname as CFString).takeRetainedValue()

        var success: DarwinBoolean = false
        CFHostStartInfoResolution(host, .addresses, nil)

        guard let addresses = CFHostGetAddressing(host, &success)?.takeUnretainedValue() as NSArray?,
              success.boolValue else {
            logger.warning("CFHost DNS resolution failed", metadata: ["hostname": hostname])
            return nil
        }

        // Find first IPv4 address
        for case let addressData as NSData in addresses {
            var storage = sockaddr_storage()
            addressData.getBytes(&storage, length: MemoryLayout<sockaddr_storage>.size)

            if storage.ss_family == sa_family_t(AF_INET) {
                let addr4 = withUnsafePointer(to: &storage) {
                    $0.withMemoryRebound(to: sockaddr_in.self, capacity: 1) { $0.pointee }
                }

                let ipString = String(cString: inet_ntoa(addr4.sin_addr))
                return ipString
            }
        }

        logger.warning("No IPv4 address found for hostname", metadata: ["hostname": hostname])
        return nil
    }
}

// MARK: - Simplified Architecture Notes

/**
 * ARCHITECTURE SIMPLIFICATION NOTES:
 *
 * Removed Components:
 * - TUNDeviceManager: NetworkExtension's packetFlow handles packet processing automatically
 * - RouteManager: NetworkExtension's NEIPv4Settings handles all routing configuration
 * - DNSManager: NetworkExtension's NEDNSSettings handles all DNS configuration
 * - TUNDeviceDelegate: No longer needed without TUNDeviceManager
 *
 * Benefits:
 * - Reduced code complexity by ~1400 lines
 * - Eliminated redundant abstractions
 * - Direct use of NetworkExtension APIs
 * - Simplified error handling and state management
 */

// MARK: - Configuration Structure

/**
 * NAME: VPNTunnelConfiguration
 *
 * DESCRIPTION:
 *     Configuration structure for VPN tunnel parameters.
 *     Contains all necessary settings for SDWAN connection and network configuration.
 *     Renamed to avoid conflict with NetworkExtensionProvider's TunnelConfiguration.
 *     Enhanced with routing configuration support.
 */
public struct VPNTunnelConfiguration {
    public let serverAddress: String
    public let serverPort: Int
    public let username: String
    public let password: String
    public var mtu: Int
    public let encryptionMethod: UInt8
    public var dnsServers: [String]
    public let excludedIPs: [String]
    public let tunnelIP: String
    public let routingMode: String
    public let customRoutes: [String]

    public init(
        serverAddress: String,
        serverPort: Int,
        username: String,
        password: String,
        mtu: Int = 1400,
        encryptionMethod: UInt8 = 0,
        dnsServers: [String] = ["*******", "*******"],
        excludedIPs: [String] = [],
        tunnelIP: String = "********",
        routingMode: String = "all",
        customRoutes: [String] = []
    ) {
        self.serverAddress = serverAddress
        self.serverPort = serverPort
        self.username = username
        self.password = password
        self.mtu = mtu
        self.encryptionMethod = encryptionMethod
        self.dnsServers = dnsServers
        self.excludedIPs = excludedIPs
        self.tunnelIP = tunnelIP
        self.routingMode = routingMode
        self.customRoutes = customRoutes
    }
}

// MARK: - VPN Extension Communication

extension VPNPacketTunnelProvider {

    /**
     * NAME: initializeServerManagerForCommunication
     *
     * DESCRIPTION:
     *     为通信目的初始化ServerManager（不需要完整的VPN组件）
     *     这允许VPN Extension在未连接状态下也能处理ping请求
     */
    private func initializeServerManagerForCommunication() async throws {
        logger.info("VPN Extension: Initializing ServerManager for communication purposes")

        // 创建基本的ServerManager实例（使用与正常初始化相同的方式）
        let serverManager = ServerManager(logger: logger)
        self.serverManager = serverManager

        logger.info("VPN Extension: ServerManager initialized for communication")
    }

    /**
     * NAME: setupDarwinNotifications
     *
     * DESCRIPTION:
     *     设置Darwin Notifications监听，用于接收主应用的请求
     */
    private func setupDarwinNotifications() {
        logger.info("Setting up Darwin Notifications for VPN Extension communication")

        notificationObserver = VPNExtensionNotificationObserver()

        // 监听ping请求
        notificationObserver?.observe(.pingRequest) { [weak self] in
            Task {
                await self?.handlePingRequest()
            }
        }

        // 监听服务器列表更新请求
        notificationObserver?.observe(.serverListUpdateRequest) { [weak self] in
            Task {
                await self?.handleServerListUpdateRequest()
            }
        }

        // 监听服务器选择请求
        notificationObserver?.observe(.serverSelectionRequest) { [weak self] in
            Task {
                await self?.handleServerSelectionRequest()
            }
        }

        logger.info("Darwin Notifications setup completed")
    }

    /**
     * NAME: handlePingRequest
     *
     * DESCRIPTION:
     *     处理主应用发送的ping请求
     */
    private func handlePingRequest() async {
        logger.info("VPN Extension: Handling ping request from main app")
        Swift.print("🔍 [PING_DEBUG] VPN Extension: handlePingRequest() - START")
        NSLog("🔍 [PING_DEBUG] VPN Extension: handlePingRequest() - START")

        // 如果ServerManager还没有初始化，先初始化它
        if serverManager == nil {
            logger.info("VPN Extension: ServerManager not initialized, initializing for ping request")
            do {
                try await initializeServerManagerForCommunication()
            } catch {
                logger.error("VPN Extension: Failed to initialize ServerManager for ping", metadata: [
                    "error": "\(error.localizedDescription)"
                ])
                VPNExtensionNotificationSender.send(.errorOccurred)
                return
            }
        }

        guard let serverManager = serverManager else {
            logger.error("VPN Extension: ServerManager still not available after initialization")
            VPNExtensionNotificationSender.send(.errorOccurred)
            return
        }

        // 检查是否有服务器列表，如果没有则尝试从App Group加载
        let currentServers = await serverManager.getServers()
        if currentServers.isEmpty {
            logger.info("VPN Extension: No servers in ServerManager, attempting to load from App Group")

            // 尝试从App Group加载服务器列表
            if let savedServers = VPNExtensionAppGroupManager.load([ServerInfo].self, forKey: .serverList),
               !savedServers.isEmpty {
                logger.info("VPN Extension: Loading servers from App Group", metadata: [
                    "servers_count": "\(savedServers.count)"
                ])

                do {
                    try await serverManager.updateServerList(savedServers)
                    logger.info("VPN Extension: Successfully updated ServerManager with servers from App Group")
                } catch {
                    handleCommunicationError(error, operation: "ping", context: [
                        "step": "update_server_manager_from_app_group",
                        "servers_count": "\(savedServers.count)"
                    ])
                    return
                }
            } else {
                logger.warning("VPN Extension: No servers available in App Group, ping operation cannot proceed")

                // 保存空的ping结果
                let emptyResults: [String: PingResult] = [:]
                VPNExtensionAppGroupManager.save(emptyResults, forKey: .pingResults)

                // 更新时间戳
                let timestamp = Date().timeIntervalSince1970
                VPNExtensionAppGroupManager.saveString("\(timestamp)", forKey: .lastUpdateTime)

                // 发送完成通知（即使没有服务器也要通知主应用）
                VPNExtensionNotificationSender.send(.pingCompleted)

                logger.info("VPN Extension: Ping request completed with no servers available")
                return
            }
        }

        // 执行ping操作
        logger.info("VPN Extension: Starting ping operation", metadata: [
            "servers_count": "\(await serverManager.getServers().count)"
        ])
        await serverManager.pingAllServers()

        // 获取ping结果
        let pingResults = await serverManager.getPingResults()

        // 保存ping结果到App Group
        VPNExtensionAppGroupManager.save(pingResults, forKey: .pingResults)

        // 更新时间戳
        let timestamp = Date().timeIntervalSince1970
        VPNExtensionAppGroupManager.saveString("\(timestamp)", forKey: .lastUpdateTime)

        // 发送完成通知
        VPNExtensionNotificationSender.send(.pingCompleted)
        Swift.print("🔍 [PING_DEBUG] VPN Extension: Sent pingCompleted notification")
        NSLog("🔍 [PING_DEBUG] VPN Extension: Sent pingCompleted notification")

        logger.info("VPN Extension: Ping request completed successfully", metadata: [
            "results_count": "\(pingResults.count)"
        ])
    }

    /**
     * NAME: handleServerListUpdateRequest
     *
     * DESCRIPTION:
     *     处理主应用发送的服务器列表更新请求
     *     从App Group读取主应用传递的新服务器列表并更新ServerManager
     */
    private func handleServerListUpdateRequest() async {
        logger.info("VPN Extension: Handling server list update request from main app")

        guard let serverManager = serverManager else {
            logger.error("VPN Extension: ServerManager not available for server list update")
            VPNExtensionNotificationSender.send(.errorOccurred)
            return
        }

        // 从App Group读取主应用传递的服务器列表
        guard let newServers = VPNExtensionAppGroupManager.load([ServerInfo].self, forKey: .serverList),
              !newServers.isEmpty else {
            logger.error("VPN Extension: Failed to load server list from App Group or list is empty")
            VPNExtensionNotificationSender.send(.errorOccurred)
            return
        }

        logger.info("VPN Extension: Loaded new server list from App Group", metadata: [
            "servers_count": "\(newServers.count)"
        ])

        // 更新ServerManager中的服务器列表
        do {
            try await serverManager.updateServerList(newServers)
            logger.info("VPN Extension: Successfully updated ServerManager with new server list")
        } catch {
            logger.error("VPN Extension: Failed to update ServerManager with new server list", metadata: [
                "error": "\(error.localizedDescription)"
            ])
            VPNExtensionNotificationSender.send(.errorOccurred)
            return
        }

        // 更新时间戳
        let timestamp = Date().timeIntervalSince1970
        VPNExtensionAppGroupManager.saveString("\(timestamp)", forKey: .lastUpdateTime)

        // 发送完成通知
        VPNExtensionNotificationSender.send(.serverListUpdated)

        logger.info("VPN Extension: Server list update completed successfully", metadata: [
            "servers_count": "\(newServers.count)"
        ])
    }

    /**
     * NAME: handleServerSelectionRequest
     *
     * DESCRIPTION:
     *     处理主应用发送的服务器选择请求
     *     实现智能服务器选择逻辑，优先选择延迟最低的在线服务器
     */
    private func handleServerSelectionRequest() async {
        logger.info("VPN Extension: Handling server selection request from main app")

        guard let serverManager = serverManager else {
            logger.error("VPN Extension: ServerManager not available for server selection")
            VPNExtensionNotificationSender.send(.errorOccurred)
            return
        }

        // 获取当前服务器列表
        let servers = await serverManager.getServers()

        if servers.isEmpty {
            logger.warning("VPN Extension: No servers available for selection")
            VPNExtensionNotificationSender.send(.errorOccurred)
            return
        }

        // 执行服务器选择逻辑
        let selectedServer = selectBestServer(from: servers)

        if let selectedServer = selectedServer {
            logger.info("VPN Extension: Selected best server", metadata: [
                "server_id": selectedServer.id,
                "server_name": selectedServer.name,
                "ping": "\(selectedServer.ping)",
                "status": selectedServer.status.rawValue
            ])

            // 保存选中的服务器到App Group
            VPNExtensionAppGroupManager.save(selectedServer, forKey: .currentServer)

            // 更新时间戳
            let timestamp = Date().timeIntervalSince1970
            VPNExtensionAppGroupManager.saveString("\(timestamp)", forKey: .lastUpdateTime)

            // 发送服务器选择完成通知
            VPNExtensionNotificationSender.send(.connectionStateChanged)

            logger.info("VPN Extension: Server selection completed successfully")
        } else {
            logger.error("VPN Extension: Failed to select any server")
            VPNExtensionNotificationSender.send(.errorOccurred)
        }
    }

    /**
     * NAME: selectBestServer
     *
     * DESCRIPTION:
     *     从服务器列表中选择最佳服务器
     *     选择逻辑：优先选择在线服务器，然后按延迟排序
     *
     * PARAMETERS:
     *     servers - 可用服务器列表
     *
     * RETURNS:
     *     ServerInfo? - 选中的最佳服务器，如果没有可用服务器则返回nil
     */
    private func selectBestServer(from servers: [ServerInfo]) -> ServerInfo? {
        logger.info("VPN Extension: Selecting best server from available servers", metadata: [
            "total_servers": "\(servers.count)"
        ])

        // 过滤在线服务器
        let onlineServers = servers.filter { $0.status == .online }

        if onlineServers.isEmpty {
            logger.warning("VPN Extension: No online servers available, falling back to all servers")
            // 如果没有在线服务器，从所有服务器中选择
            return servers.first
        }

        logger.info("VPN Extension: Found online servers", metadata: [
            "online_count": "\(onlineServers.count)"
        ])

        // 过滤有效ping值的服务器（ping > 0）
        let serversWithValidPing = onlineServers.filter { $0.ping > 0 }

        if serversWithValidPing.isEmpty {
            logger.info("VPN Extension: No servers with valid ping, selecting first online server")
            return onlineServers.first
        }

        // 按ping值排序，选择延迟最低的服务器
        let sortedServers = serversWithValidPing.sorted { $0.ping < $1.ping }
        let bestServer = sortedServers.first

        if let bestServer = bestServer {
            logger.info("VPN Extension: Selected server with best ping", metadata: [
                "server_id": bestServer.id,
                "server_name": bestServer.name,
                "ping": "\(bestServer.ping)ms"
            ])
        }

        return bestServer
    }

    // MARK: - Error Handling and Recovery

    /**
     * NAME: handleCommunicationError
     *
     * DESCRIPTION:
     *     统一处理通信错误，记录错误信息并尝试恢复
     *
     * PARAMETERS:
     *     error - 错误信息
     *     operation - 发生错误的操作名称
     *     context - 错误上下文信息
     */
    private func handleCommunicationError(_ error: Error, operation: String, context: [String: String] = [:]) {
        logger.error("VPN Extension communication error", metadata: [
            "operation": operation,
            "error": "\(error.localizedDescription)",
            "context": "\(context)"
        ])

        // 保存错误信息到App Group
        let errorInfo: [String: Any] = [
            "operation": operation,
            "error": error.localizedDescription,
            "timestamp": Date().timeIntervalSince1970,
            "context": context
        ]

        if let errorData = try? JSONSerialization.data(withJSONObject: errorInfo),
           let errorString = String(data: errorData, encoding: .utf8) {
            VPNExtensionAppGroupManager.saveString(errorString, forKey: .lastError)
        }

        // 发送错误通知给主应用
        VPNExtensionNotificationSender.send(.errorOccurred)

        // 尝试错误恢复
        Task {
            await attemptErrorRecovery(for: operation)
        }
    }

    /**
     * NAME: attemptErrorRecovery
     *
     * DESCRIPTION:
     *     尝试从错误中恢复，根据不同的操作类型采取不同的恢复策略
     *
     * PARAMETERS:
     *     operation - 发生错误的操作名称
     */
    private func attemptErrorRecovery(for operation: String) async {
        logger.info("VPN Extension: Attempting error recovery", metadata: [
            "operation": operation
        ])

        switch operation {
        case "ping":
            // ping操作失败时，尝试重新加载服务器列表
            await recoverFromPingFailure()

        case "server_list_update":
            // 服务器列表更新失败时，尝试使用缓存数据
            await recoverFromServerListUpdateFailure()

        case "server_selection":
            // 服务器选择失败时，尝试使用默认服务器
            await recoverFromServerSelectionFailure()

        default:
            logger.info("VPN Extension: No specific recovery strategy for operation", metadata: [
                "operation": operation
            ])
        }
    }

    /**
     * NAME: recoverFromPingFailure
     *
     * DESCRIPTION:
     *     从ping操作失败中恢复
     */
    private func recoverFromPingFailure() async {
        logger.info("VPN Extension: Recovering from ping failure")

        // 尝试重新加载服务器列表
        if let savedServers = VPNExtensionAppGroupManager.load([ServerInfo].self, forKey: .serverList),
           !savedServers.isEmpty,
           let serverManager = serverManager {

            do {
                try await serverManager.updateServerList(savedServers)
                logger.info("VPN Extension: Successfully reloaded server list for ping recovery")

                // 重新尝试ping操作
                await serverManager.pingAllServers()
                let pingResults = await serverManager.getPingResults()
                VPNExtensionAppGroupManager.save(pingResults, forKey: .pingResults)
                VPNExtensionNotificationSender.send(.pingCompleted)

                logger.info("VPN Extension: Ping recovery completed successfully")
            } catch {
                logger.error("VPN Extension: Ping recovery failed", metadata: [
                    "error": "\(error.localizedDescription)"
                ])
            }
        }
    }

    /**
     * NAME: recoverFromServerListUpdateFailure
     *
     * DESCRIPTION:
     *     从服务器列表更新失败中恢复
     */
    private func recoverFromServerListUpdateFailure() async {
        logger.info("VPN Extension: Recovering from server list update failure")

        // 检查是否有缓存的服务器列表可以使用
        if let cachedServers = VPNExtensionAppGroupManager.load([ServerInfo].self, forKey: .serverList),
           !cachedServers.isEmpty {

            logger.info("VPN Extension: Using cached server list for recovery", metadata: [
                "cached_servers_count": "\(cachedServers.count)"
            ])

            // 通知主应用使用缓存数据
            VPNExtensionNotificationSender.send(.serverListUpdated)
        } else {
            logger.warning("VPN Extension: No cached server list available for recovery")
        }
    }

    /**
     * NAME: recoverFromServerSelectionFailure
     *
     * DESCRIPTION:
     *     从服务器选择失败中恢复
     */
    private func recoverFromServerSelectionFailure() async {
        logger.info("VPN Extension: Recovering from server selection failure")

        guard let serverManager = serverManager else {
            logger.error("VPN Extension: ServerManager not available for server selection recovery")
            return
        }

        // 获取服务器列表
        let servers = await serverManager.getServers()

        if !servers.isEmpty {
            // 选择第一个可用服务器作为默认选择
            let defaultServer = servers.first!

            logger.info("VPN Extension: Using default server for recovery", metadata: [
                "server_id": defaultServer.id,
                "server_name": defaultServer.name
            ])

            // 保存默认服务器
            VPNExtensionAppGroupManager.save(defaultServer, forKey: .currentServer)
            VPNExtensionNotificationSender.send(.connectionStateChanged)
        } else {
            logger.error("VPN Extension: No servers available for selection recovery")
        }
    }

    /**
     * NAME: fetchServerListFromURL
     *
     * DESCRIPTION:
     *     从指定URL获取服务器列表
     *     实现与主应用相同的HTTP服务器列表获取功能
     *
     * PARAMETERS:
     *     url - 服务器列表URL
     *
     * RETURNS:
     *     [ServerInfo] - 获取到的服务器列表
     *
     * THROWS:
     *     Error - 网络请求或解析错误
     */
    private func fetchServerListFromURL(_ url: String) async throws -> [ServerInfo] {
        logger.info("VPN Extension: Fetching server list from URL", metadata: ["url": url])

        guard let requestUrl = URL(string: url) else {
            throw NSError(domain: "VPNExtensionError", code: 1, userInfo: [
                NSLocalizedDescriptionKey: "Invalid server list URL format"
            ])
        }

        var request = URLRequest(url: requestUrl)
        request.timeoutInterval = 10.0
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue("*/*", forHTTPHeaderField: "User-Agent")
        request.setValue("no-cache", forHTTPHeaderField: "Cache-Control")

        // Create URL session for HTTP requests
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 10.0
        config.timeoutIntervalForResource = 30.0
        let session = URLSession(configuration: config)

        // Fetch server list
        let (data, response) = try await session.data(for: request)

        // Check HTTP response
        guard let httpResponse = response as? HTTPURLResponse else {
            throw NSError(domain: "VPNExtensionError", code: 2, userInfo: [
                NSLocalizedDescriptionKey: "Invalid HTTP response"
            ])
        }

        guard httpResponse.statusCode == 200 else {
            throw NSError(domain: "VPNExtensionError", code: 3, userInfo: [
                NSLocalizedDescriptionKey: "HTTP request failed with status code: \(httpResponse.statusCode)"
            ])
        }

        logger.info("VPN Extension: Server list fetched successfully, parsing JSON")

        // Parse JSON response (using same logic as PlatformChannelHandler)
        let servers = try parseServerListJSON(data)

        logger.info("VPN Extension: Parsed server list successfully", metadata: [
            "servers_count": "\(servers.count)"
        ])

        return servers
    }

    /**
     * NAME: parseServerListJSON
     *
     * DESCRIPTION:
     *     解析服务器列表JSON数据
     *     与PlatformChannelHandler中的解析逻辑保持一致
     *
     * PARAMETERS:
     *     data - JSON数据
     *
     * RETURNS:
     *     [ServerInfo] - 解析后的服务器列表
     *
     * THROWS:
     *     Error - JSON解析错误
     */
    private func parseServerListJSON(_ data: Data) throws -> [ServerInfo] {
        guard let jsonDict = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            throw NSError(domain: "VPNExtensionError", code: 4, userInfo: [
                NSLocalizedDescriptionKey: "Invalid JSON format"
            ])
        }

        var serverListArray: [[String: Any]]

        if let serverlist = jsonDict["serverlist"] as? [[String: Any]] {
            // Format: {"version": "1.0", "serverlist": [...]}
            serverListArray = serverlist
        } else if let data = jsonDict["data"] as? [[String: Any]] {
            // Format: {"success": true, "data": [...]}
            serverListArray = data
        } else {
            throw NSError(domain: "VPNExtensionError", code: 5, userInfo: [
                NSLocalizedDescriptionKey: "No serverlist or data array found"
            ])
        }

        var servers: [ServerInfo] = []

        for serverDict in serverListArray {
            // Parse server data (same logic as PlatformChannelHandler)
            guard let id = serverDict["id"] as? Int,
                  let name = serverDict["name"] as? String,
                  let serverName = serverDict["server_name"] as? String ?? serverDict["serverName"] as? String else {
                logger.warning("VPN Extension: Missing required server fields", metadata: [
                    "server_data": "\(serverDict)"
                ])
                continue
            }

            let serverPort = serverDict["server_port"] as? Int ?? serverDict["serverPort"] as? Int ?? 8000
            let nameEn = serverDict["name_en"] as? String ?? name
            let isAuto = serverDict["isauto"] as? Bool ?? false

            let server = ServerInfo(
                id: String(id),
                name: name,
                nameEn: nameEn,
                serverName: serverName,
                serverPort: serverPort,
                isAuto: isAuto
            )

            servers.append(server)
        }

        return servers
    }

    /**
     * NAME: updateServerListFromConfiguredURL
     *
     * DESCRIPTION:
     *     从配置的URL更新服务器列表
     *     这个方法可以被定期调用以保持服务器列表的最新状态
     */
    private func updateServerListFromConfiguredURL() async {
        logger.info("VPN Extension: Starting server list update from configured URL")

        // 从App Group读取配置的服务器列表URL
        guard let serverListURL = VPNExtensionAppGroupManager.loadString(forKey: .serverListURL),
              !serverListURL.isEmpty else {
            logger.warning("VPN Extension: No server list URL configured, skipping update")
            return
        }

        logger.info("VPN Extension: Fetching server list from configured URL", metadata: [
            "url": serverListURL
        ])

        do {
            // 从URL获取服务器列表
            let newServers = try await fetchServerListFromURL(serverListURL)

            guard let serverManager = serverManager else {
                logger.error("VPN Extension: ServerManager not available for server list update")
                return
            }

            // 更新ServerManager中的服务器列表
            try await serverManager.updateServerList(newServers)

            // 保存到App Group
            VPNExtensionAppGroupManager.save(newServers, forKey: .serverList)

            // 更新时间戳
            let timestamp = Date().timeIntervalSince1970
            VPNExtensionAppGroupManager.saveString("\(timestamp)", forKey: .lastUpdateTime)

            // 发送更新通知给主应用
            VPNExtensionNotificationSender.send(.serverListUpdated)

            logger.info("VPN Extension: Server list update completed successfully", metadata: [
                "servers_count": "\(newServers.count)",
                "url": serverListURL
            ])

        } catch {
            logger.error("VPN Extension: Failed to update server list from URL", metadata: [
                "url": serverListURL,
                "error": "\(error.localizedDescription)"
            ])

            // 发送错误通知
            VPNExtensionNotificationSender.send(.errorOccurred)
        }
    }

    /**
     * NAME: cleanupDarwinNotifications
     *
     * DESCRIPTION:
     *     清理Darwin Notifications监听
     */
    private func cleanupDarwinNotifications() {
        logger.info("Cleaning up Darwin Notifications")
        notificationObserver?.removeAllObservers()
        notificationObserver = nil
    }
}