/**
 * FILE: VPNExtensionCommunication.swift
 * PROJECT: ItForce VPN
 * DESCRIPTION: VPN Extension与主应用通信的常量定义和工具方法
 * AUTHOR: ItForce Team
 * DATE: 2024-12-19
 */

import Foundation
import OSLog

// MARK: - Darwin Notifications常量

/**
 * NAME: VPNExtensionNotification
 *
 * DESCRIPTION:
 *     Darwin Notifications事件常量定义
 *     用于VPN Extension与主应用之间的实时通信
 */
public enum VPNExtensionNotification: String, CaseIterable {
    // 主应用 → VPN Extension 请求
    case pingRequest = "com.panabit.vpn.ping.request"
    case serverListUpdateRequest = "com.panabit.vpn.serverlist.update.request"
    case serverSelectionRequest = "com.panabit.vpn.server.select.request"
    
    // VPN Extension → 主应用 通知
    case pingCompleted = "com.panabit.vpn.ping.completed"
    case serverListUpdated = "com.panabit.vpn.serverlist.updated"
    case connectionStateChanged = "com.panabit.vpn.connection.changed"
    case trafficStatsUpdated = "com.panabit.vpn.traffic.updated"
    case errorOccurred = "com.panabit.vpn.error.occurred"
}

// MARK: - App Group存储键值常量

/**
 * NAME: VPNExtensionAppGroupKey
 *
 * DESCRIPTION:
 *     App Group UserDefaults存储键值常量
 *     用于VPN Extension与主应用之间的数据共享
 */
public enum VPNExtensionAppGroupKey: String, CaseIterable {
    // 服务器相关数据
    case serverList = "vpn_server_list"
    case pingResults = "vpn_ping_results"
    case currentServer = "vpn_current_server"
    
    // 连接状态数据
    case connectionState = "vpn_connection_state"
    case trafficStatistics = "vpn_traffic_statistics"
    
    // 配置数据
    case lastUpdateTime = "vpn_last_update_time"
    case dataVersion = "vpn_data_version"
    case serverListURL = "vpn_server_list_url"
    
    // 错误信息
    case lastError = "vpn_last_error"
}

// MARK: - App Group标识符

/**
 * NAME: VPNExtensionAppGroup
 *
 * DESCRIPTION:
 *     App Group标识符常量
 */
public struct VPNExtensionAppGroup {
    public static let identifier = "group.com.panabit.PanabitClient"
}

// MARK: - Darwin Notifications工具方法

/**
 * NAME: VPNExtensionNotificationSender
 *
 * DESCRIPTION:
 *     Darwin Notifications发送工具类
 *     提供简单的通知发送方法
 */
public struct VPNExtensionNotificationSender {
    
    /**
     * NAME: send
     *
     * DESCRIPTION:
     *     发送Darwin Notification
     *
     * PARAMETERS:
     *     notification - 要发送的通知类型
     */
    public static func send(_ notification: VPNExtensionNotification) {
        let notificationName = CFNotificationName(rawValue: notification.rawValue as CFString)
        CFNotificationCenterPostNotification(
            CFNotificationCenterGetDarwinNotifyCenter(),
            notificationName,
            nil,
            nil,
            true
        )
    }
}

// MARK: - Darwin Notifications监听工具

/**
 * NAME: VPNExtensionNotificationObserver
 *
 * DESCRIPTION:
 *     Darwin Notifications监听工具类
 *     提供简单的通知监听方法
 */
public class VPNExtensionNotificationObserver {
    
    private var observers: [String: Any] = [:]
    
    /**
     * NAME: observe
     *
     * DESCRIPTION:
     *     监听Darwin Notification
     *
     * PARAMETERS:
     *     notification - 要监听的通知类型
     *     handler - 通知处理回调
     */
    public func observe(_ notification: VPNExtensionNotification, handler: @escaping () -> Void) {
        let center = CFNotificationCenterGetDarwinNotifyCenter()
        let observer = Unmanaged.passRetained(NotificationHandler(handler: handler))
        
        CFNotificationCenterAddObserver(
            center,
            observer.toOpaque(),
            { _, observer, name, _, _ in
                guard let observer = observer else { return }
                let handler = Unmanaged<NotificationHandler>.fromOpaque(observer).takeUnretainedValue()
                handler.handle()
            },
            notification.rawValue as CFString,
            nil,
            .deliverImmediately
        )
        
        observers[notification.rawValue] = observer
    }
    
    /**
     * NAME: removeObserver
     *
     * DESCRIPTION:
     *     移除Darwin Notification监听
     *
     * PARAMETERS:
     *     notification - 要移除监听的通知类型
     */
    public func removeObserver(_ notification: VPNExtensionNotification) {
        let center = CFNotificationCenterGetDarwinNotifyCenter()
        
        if let observer = observers[notification.rawValue] {
            CFNotificationCenterRemoveObserver(
                center,
                Unmanaged<NotificationHandler>.fromOpaque(observer as! UnsafeRawPointer).toOpaque(),
                CFNotificationName(rawValue: notification.rawValue as CFString),
                nil
            )
            
            // 释放observer
            Unmanaged<NotificationHandler>.fromOpaque(observer as! UnsafeRawPointer).release()
            observers.removeValue(forKey: notification.rawValue)
        }
    }
    
    /**
     * NAME: removeAllObservers
     *
     * DESCRIPTION:
     *     移除所有Darwin Notification监听
     */
    public func removeAllObservers() {
        for notification in VPNExtensionNotification.allCases {
            removeObserver(notification)
        }
    }
    
    deinit {
        removeAllObservers()
    }
}

// MARK: - 内部辅助类

/**
 * NAME: NotificationHandler
 *
 * DESCRIPTION:
 *     Darwin Notification处理器内部类
 */
private class NotificationHandler {
    let handler: () -> Void
    
    init(handler: @escaping () -> Void) {
        self.handler = handler
    }
    
    func handle() {
        handler()
    }
}

// MARK: - App Group数据访问工具

/**
 * NAME: VPNExtensionAppGroupManager
 *
 * DESCRIPTION:
 *     App Group数据访问管理器
 *     提供简化的数据读写方法
 */
public struct VPNExtensionAppGroupManager {
    
    private static var userDefaults: UserDefaults? {
        return UserDefaults(suiteName: VPNExtensionAppGroup.identifier)
    }
    
    /**
     * NAME: save
     *
     * DESCRIPTION:
     *     保存数据到App Group
     *
     * PARAMETERS:
     *     data - 要保存的数据
     *     key - 存储键值
     */
    public static func save<T: Codable>(_ data: T, forKey key: VPNExtensionAppGroupKey) {
        guard let userDefaults = userDefaults else { return }
        
        do {
            let encodedData = try JSONEncoder().encode(data)
            userDefaults.set(encodedData, forKey: key.rawValue)
            userDefaults.synchronize()
        } catch {
            // 静默处理编码错误
        }
    }
    
    /**
     * NAME: load
     *
     * DESCRIPTION:
     *     从App Group加载数据
     *
     * PARAMETERS:
     *     type - 数据类型
     *     key - 存储键值
     *
     * RETURNS:
     *     T? - 加载的数据，失败时返回nil
     */
    public static func load<T: Codable>(_ type: T.Type, forKey key: VPNExtensionAppGroupKey) -> T? {
        guard let userDefaults = userDefaults,
              let data = userDefaults.data(forKey: key.rawValue) else {
            return nil
        }
        
        do {
            return try JSONDecoder().decode(type, from: data)
        } catch {
            return nil
        }
    }
    
    /**
     * NAME: remove
     *
     * DESCRIPTION:
     *     从App Group移除数据
     *
     * PARAMETERS:
     *     key - 存储键值
     */
    public static func remove(forKey key: VPNExtensionAppGroupKey) {
        guard let userDefaults = userDefaults else { return }
        userDefaults.removeObject(forKey: key.rawValue)
        userDefaults.synchronize()
    }
    
    /**
     * NAME: saveString
     *
     * DESCRIPTION:
     *     保存字符串到App Group
     *
     * PARAMETERS:
     *     value - 字符串值
     *     key - 存储键值
     */
    public static func saveString(_ value: String, forKey key: VPNExtensionAppGroupKey) {
        guard let userDefaults = userDefaults else { return }
        userDefaults.set(value, forKey: key.rawValue)
        userDefaults.synchronize()
    }
    
    /**
     * NAME: loadString
     *
     * DESCRIPTION:
     *     从App Group加载字符串
     *
     * PARAMETERS:
     *     key - 存储键值
     *
     * RETURNS:
     *     String? - 加载的字符串，失败时返回nil
     */
    public static func loadString(forKey key: VPNExtensionAppGroupKey) -> String? {
        guard let userDefaults = userDefaults else { return nil }
        return userDefaults.string(forKey: key.rawValue)
    }
}
