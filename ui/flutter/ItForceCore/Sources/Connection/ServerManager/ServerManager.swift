/**
 * FILE: ServerManager.swift
 *
 * DESCRIPTION:
 *     Server manager implementation for SDWAN ZZVPN protocol.
 *     Manages server list, server selection, health monitoring, and failover.
 *     Compatible with Go backend server manager functionality.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import Network
import OSLog

/**
 * NAME: ServerInfo
 *
 * DESCRIPTION:
 *     Server information structure matching Go backend Server type.
 *     Contains server connection details and status information.
 *
 * PROPERTIES:
 *     id - Unique server identifier
 *     name - Server display name
 *     nameEn - Server English name
 *     serverName - Server address (domain or IP)
 *     serverPort - Server port number
 *     isAuto - Whether this is an auto-routing server
 *     ping - Latency in milliseconds
 *     status - Server status (online, offline, unknown)
 *     lastCheck - Last status check timestamp
 */
public struct ServerInfo: Sendable, Identifiable, Codable {
    public let id: String
    public let name: String
    public let nameEn: String
    public let serverName: String
    public let serverPort: Int
    public let isAuto: Bool
    public var ping: Int
    public var status: ServerStatus
    public var lastCheck: Date
    
    public init(
        id: String,
        name: String,
        nameEn: String,
        serverName: String,
        serverPort: Int,
        isAuto: Bool = false,
        ping: Int = 0,
        status: ServerStatus = .unknown,
        lastCheck: Date = Date()
    ) {
        self.id = id
        self.name = name
        self.nameEn = nameEn
        self.serverName = serverName
        self.serverPort = serverPort
        self.isAuto = isAuto
        self.ping = ping
        self.status = status
        self.lastCheck = lastCheck
    }
}

/**
 * NAME: ServerStatus
 *
 * DESCRIPTION:
 *     Server status enumeration.
 *     Represents current server availability status.
 */
public enum ServerStatus: String, CaseIterable, Sendable, Codable {
    case online = "online"
    case offline = "offline"
    case unknown = "unknown"
    
    public var isAvailable: Bool {
        return self == .online
    }
}

/**
 * NAME: ServerSelectionMode
 *
 * DESCRIPTION:
 *     Server selection mode enumeration matching Go backend logic.
 *     Defines how servers are selected for connection.
 */
public enum ServerSelectionMode: String, CaseIterable, Sendable {
    case auto = "auto"              // Select best auto server (isAuto=true) by lowest latency
    case manual = "manual"          // Manual server selection by user/frontend
}

/**
 * NAME: PingResult
 *
 * DESCRIPTION:
 *     Ping operation result structure.
 *     Contains ping latency and status information.
 *
 * PROPERTIES:
 *     serverID - Server identifier
 *     latency - Ping latency in milliseconds
 *     isSuccess - Whether ping was successful
 *     timestamp - Ping timestamp
 */
public struct PingResult: Sendable, Codable {
    public let serverID: String
    public let latency: Int
    public let isSuccess: Bool
    public let timestamp: Date

    public init(serverID: String, latency: Int, isSuccess: Bool) {
        self.serverID = serverID
        self.latency = latency
        self.isSuccess = isSuccess
        self.timestamp = Date()
    }
}

/**
 * NAME: ServerConfiguration
 *
 * DESCRIPTION:
 *     Server manager configuration structure.
 *     Contains intervals, timeouts, and monitoring settings.
 *
 * PROPERTIES:
 *     updateInterval - Server list update interval
 *     pingInterval - Ping operation interval
 *     pingTimeout - Ping operation timeout
 *     offlineThreshold - Server offline threshold

 */
public struct ServerConfiguration: Sendable {
    public let updateInterval: TimeInterval
    public let pingInterval: TimeInterval
    public let pingTimeout: TimeInterval
    public let offlineThreshold: TimeInterval

    
    public init(
        updateInterval: TimeInterval = 3600,  // 1 hour
        pingInterval: TimeInterval = 60,      // 60 seconds for reduced network usage
        pingTimeout: TimeInterval = 5,        // 5 seconds
        offlineThreshold: TimeInterval = 900, // 15 minutes

    ) {
        self.updateInterval = updateInterval
        self.pingInterval = pingInterval
        self.pingTimeout = pingTimeout
        self.offlineThreshold = offlineThreshold

    }
    
    public static let `default` = ServerConfiguration()
}

/**
 * NAME: ServerUpdateCallback
 *
 * DESCRIPTION:
 *     Type alias for server list update callback functions.
 *     Called when server list is updated.
 */
public typealias ServerUpdateCallback = @Sendable ([ServerInfo]) -> Void

/**
 * NAME: ServerStatusCallback
 *
 * DESCRIPTION:
 *     Type alias for server status change callback functions.
 *     Called when server status changes.
 */
public typealias ServerStatusCallback = @Sendable (String, ServerStatus) -> Void



/**
 * NAME: ServerManagerError
 *
 * DESCRIPTION:
 *     Server manager specific errors.
 *     Provides detailed error information for server operations.
 */
public enum ServerManagerError: Error, LocalizedError, Sendable {
    case noServersAvailable
    case serverNotFound(String)
    case pingFailed(String)
    case updateFailed(String)
    case configurationInvalid(String)
    case networkUnavailable
    case timeout
    
    public var errorDescription: String? {
        switch self {
        case .noServersAvailable:
            return "No servers available for connection"
        case .serverNotFound(let id):
            return "Server not found: \(id)"
        case .pingFailed(let reason):
            return "Ping operation failed: \(reason)"
        case .updateFailed(let reason):
            return "Server list update failed: \(reason)"
        case .configurationInvalid(let reason):
            return "Invalid configuration: \(reason)"
        case .networkUnavailable:
            return "Network is unavailable"
        case .timeout:
            return "Operation timed out"
        }
    }
}

/**
 * NAME: ServerManager
 *
 * DESCRIPTION:
 *     Thread-safe server manager using Swift Actor model.
 *     Manages server list, selection, health monitoring, and failover.
 *     Provides compatibility with Go backend server manager functionality.
 *
 * PROPERTIES:
 *     servers - Current server list
 *     currentServer - Currently selected server
 *     selectionStrategy - Server selection strategy
 *     pingResults - Ping results cache
 *     configuration - Manager configuration
 *     updateCallbacks - Server update callback functions
 *     statusCallbacks - Status change callback functions
 *     networkCallbacks - Network change callback functions
 *     logger - Logger instance
 */
public actor ServerManager {
    // MARK: - Server Management Properties

    private var servers: [ServerInfo] = []
    private var currentServer: ServerInfo?
    private var selectionMode: ServerSelectionMode = .auto
    private var pingResults: [String: PingResult] = [:]

    // MARK: - IP Cache Management Properties

    /// Simple IP cache for DNS resolution (hostname -> IP)
    private var serverIPCache: [String: String] = [:]

    // MARK: - Configuration and Dependencies

    private let configuration: ServerConfiguration
    private let logger: LoggerProtocol
    
    // MARK: - Callback Properties
    
    private var updateCallbacks: [ServerUpdateCallback] = []
    private var statusCallbacks: [ServerStatusCallback] = []
    
    // MARK: - Background Task Management
    
    private var updateTask: Task<Void, Never>?
    private var pingTask: Task<Void, Never>?
    private var isRunning: Bool = false
    
    // MARK: - Initialization
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Initializes server manager with configuration and logger.
     *
     * PARAMETERS:
     *     configuration - Server manager configuration
     *     logger - Logger instance for debugging
     */
    public init(configuration: ServerConfiguration = .default, logger: LoggerProtocol) {
        self.configuration = configuration
        self.logger = logger

        logger.info("Server manager initialized", metadata: [
            "update_interval": "\(configuration.updateInterval)",
            "ping_interval": "\(configuration.pingInterval)",
            "ping_timeout": "\(configuration.pingTimeout)"
        ])
    }

    /**
     * NAME: init(logger:)
     *
     * DESCRIPTION:
     *     Convenience initializer with default configuration.
     *
     * PARAMETERS:
     *     logger - Logger instance for debugging
     */
    public init(logger: LoggerProtocol) {
        self.init(configuration: .default, logger: logger)
    }

    // MARK: - Lifecycle Management

    /**
     * NAME: start
     *
     * DESCRIPTION:
     *     Starts server manager background tasks.
     *     Begins server list updates, ping monitoring, and network monitoring.
     *
     * THROWS:
     *     ServerManagerError - If start operation fails
     */
    public func start() async throws {
        guard !isRunning else {
            logger.warning("Server manager already running")
            return
        }

        logger.info("Starting server manager")
        isRunning = true

        // Start background tasks
        startUpdateTask()
        // Note: Ping task is now managed by ServerService to ensure proper delegate notifications
        // Note: Network monitoring is now handled by ConnectionManager for VPN reconnection

        logger.info("Server manager started successfully")
    }

    /**
     * NAME: stop
     *
     * DESCRIPTION:
     *     Stops server manager and cancels all background tasks.
     */
    public func stop() async {
        guard isRunning else {
            return
        }

        logger.info("Stopping server manager")
        isRunning = false

        // Cancel background tasks
        updateTask?.cancel()
        pingTask?.cancel()

        updateTask = nil
        pingTask = nil

        logger.info("Server manager stopped")
    }

    // MARK: - Server List Management

    /**
     * NAME: updateServerList
     *
     * DESCRIPTION:
     *     Updates server list from configured sources.
     *
     * PARAMETERS:
     *     newServers - New server list
     *
     * THROWS:
     *     ServerManagerError - If update operation fails
     */
    public func updateServerList(_ newServers: [ServerInfo]) async throws {
        guard !newServers.isEmpty else {
            throw ServerManagerError.updateFailed("Empty server list provided")
        }

        logger.info("Updating server list", metadata: [
            "new_count": "\(newServers.count)",
            "old_count": "\(servers.count)"
        ])

        // Update server list
        servers = newServers

        // Reset ping results for new servers
        pingResults.removeAll()

        // Notify callbacks
        let callbacks = updateCallbacks
        Task {
            for callback in callbacks {
                callback(newServers)
            }
        }

        logger.info("Server list updated successfully", metadata: [
            "server_count": "\(servers.count)"
        ])

        // Pre-resolve all server IPs in background to avoid DNS failures during connection/ping
        logger.info("Starting batch IP resolution for server list", metadata: [
            "server_count": "\(newServers.count)"
        ])

        Task {
            var successCount = 0
            for server in newServers {
                if let resolvedIP = await resolveHostnameToIP(server.serverName) {
                    serverIPCache[server.serverName] = resolvedIP

                    // Store in App Group for VPN extension access
                    storeServerIPInAppGroup(server.serverName, resolvedIP)

                    successCount += 1
                } else {
                    logger.warning("Failed to resolve server IP", metadata: [
                        "hostname": server.serverName,
                        "error": "DNS resolution failed"
                    ])
                }
            }

            logger.info("Batch IP resolution completed", metadata: [
                "total_servers": "\(newServers.count)",
                "successful_resolves": "\(successCount)",
                "failed_resolves": "\(newServers.count - successCount)"
            ])
        }
    }

    /**
     * NAME: updateServerListSilently
     *
     * DESCRIPTION:
     *     Updates server list without triggering callback notifications.
     *     Used when server list version changes but we want to defer UI updates until next ping.
     *
     * PARAMETERS:
     *     newServers - New server list
     *
     * THROWS:
     *     ServerManagerError - If update operation fails
     */
    public func updateServerListSilently(_ newServers: [ServerInfo]) async throws {
        guard !newServers.isEmpty else {
            throw ServerManagerError.updateFailed("Empty server list provided")
        }

        logger.info("Updating server list silently", metadata: [
            "new_count": "\(newServers.count)",
            "old_count": "\(servers.count)"
        ])

        // Update server list
        servers = newServers

        // Reset ping results for new servers
        pingResults.removeAll()

        // Note: Do NOT notify callbacks to avoid immediate UI updates

        logger.info("Server list updated silently", metadata: [
            "server_count": "\(servers.count)"
        ])

        // Pre-resolve all server IPs in background to avoid DNS failures during connection/ping
        logger.info("Starting batch IP resolution for server list", metadata: [
            "server_count": "\(newServers.count)"
        ])

        Task {
            var successCount = 0
            for server in newServers {
                if let resolvedIP = await resolveHostnameToIP(server.serverName) {
                    serverIPCache[server.serverName] = resolvedIP

                    // Store in App Group for VPN extension access
                    storeServerIPInAppGroup(server.serverName, resolvedIP)

                    successCount += 1
                } else {
                    logger.warning("Failed to resolve server IP", metadata: [
                        "hostname": server.serverName,
                        "error": "DNS resolution failed"
                    ])
                }
            }

            logger.info("Batch IP resolution completed", metadata: [
                "total_servers": "\(newServers.count)",
                "successful_resolves": "\(successCount)",
                "failed_resolves": "\(newServers.count - successCount)"
            ])
        }
    }

    /**
     * NAME: getServers
     *
     * DESCRIPTION:
     *     Returns current server list sorted by latency.
     *     Online servers are prioritized first, then sorted by ping time.
     *
     * RETURNS:
     *     [ServerInfo] - Server list sorted by latency
     */
    public func getServers() -> [ServerInfo] {
        // Sort servers by latency - prioritize online servers first, then sort by latency
        // Ping failed servers (latency 0) are placed at the end
        let sortedServers = servers.sorted { server1, server2 in
            // Prioritize online servers
            if server1.status == .online && server2.status != .online {
                return true
            }
            if server1.status != .online && server2.status == .online {
                return false
            }

            // If status is the same, sort by latency
            // Handle ping failed servers (latency 0) - place them at the end
            if server1.ping == 0 && server2.ping > 0 {
                return false // server1 (ping failed) goes after server2
            }
            if server1.ping > 0 && server2.ping == 0 {
                return true // server1 goes before server2 (ping failed)
            }
            if server1.ping == 0 && server2.ping == 0 {
                return false // Both ping failed, maintain original order
            }

            // Both have valid ping values, sort by latency (low to high)
            return server1.ping < server2.ping
        }

        return sortedServers
    }

    /**
     * NAME: getServer
     *
     * DESCRIPTION:
     *     Returns server by ID.
     *
     * PARAMETERS:
     *     id - Server identifier
     *
     * RETURNS:
     *     ServerInfo? - Server information or nil if not found
     */
    public func getServer(id: String) -> ServerInfo? {
        return servers.first { $0.id == id }
    }

    /**
     * NAME: getCurrentServer
     *
     * DESCRIPTION:
     *     Returns currently selected server.
     *
     * RETURNS:
     *     ServerInfo? - Current server or nil
     */
    public func getCurrentServer() -> ServerInfo? {
        return currentServer
    }

    /**
     * NAME: setCurrentServer
     *
     * DESCRIPTION:
     *     Sets currently selected server.
     *
     * PARAMETERS:
     *     server - Server to set as current
     */
    public func setCurrentServer(_ server: ServerInfo?) {
        currentServer = server

        if let server = server {
            logger.info("Current server set", metadata: [
                "server_id": server.id,
                "server_name": server.name,
                "server_address": server.serverName
            ])
        } else {
            logger.info("Current server cleared")
        }
    }

    // MARK: - Server Selection

    /**
     * NAME: selectBestServer
     *
     * DESCRIPTION:
     *     Selects best server based on current selection mode.
     *     Matches Go backend GetBestServer() functionality.
     *
     * RETURNS:
     *     ServerInfo? - Best server or nil if none available
     *
     * THROWS:
     *     ServerManagerError - If no servers available
     */
    public func selectBestServer() async throws -> ServerInfo? {
        logger.info("Selecting best server", metadata: [
            "servers_count": "\(servers.count)",
            "selection_mode": "\(selectionMode)"
        ])

        guard !servers.isEmpty else {
            logger.error("No servers available for selection")
            throw ServerManagerError.noServersAvailable
        }

        let selectedServer: ServerInfo?

        switch selectionMode {
        case .auto:
            // logger.info("Using auto selection mode") // Debug log commented for production
            selectedServer = try await selectBestAutoServer()
        case .manual:
            // logger.info("Using manual selection mode") // Debug log commented for production
            selectedServer = currentServer
        }

        if let server = selectedServer {
            logger.info("Best server selected", metadata: [
                "mode": selectionMode.rawValue,
                "server_id": server.id,
                "server_name": server.name,
                "ping": "\(server.ping)",
                "status": server.status.rawValue
            ])
        }

        return selectedServer
    }

    /**
     * NAME: selectBestAutoServer
     *
     * DESCRIPTION:
     *     Selects best auto server (isAuto = true) based on latency.
     *     Matches Go backend SelectBestAutoServer() functionality.
     *
     * RETURNS:
     *     ServerInfo? - Best auto server or nil if none available
     *
     * THROWS:
     *     ServerManagerError - If no auto servers available
     */
    public func selectBestAutoServer() async throws -> ServerInfo? {
        logger.info("Selecting best auto server", metadata: [
            "total_servers": "\(servers.count)"
        ])

        // Log all servers for debugging
        // for (index, server) in servers.enumerated() {
        //     logger.info("Server[\(index)]: id=\(server.id), name=\(server.name), isAuto=\(server.isAuto), status=\(server.status.rawValue), ping=\(server.ping)")
        // } // Debug log commented for production

        // Filter automatic servers - consider all automatic servers
        let autoServers = servers.filter { $0.isAuto }

        guard !autoServers.isEmpty else {
            logger.error("No auto servers found - all servers have isAuto=false")
            throw ServerManagerError.noServersAvailable
        }

        // Sort by latency - prioritize online servers first, then sort by latency
        let sortedServers = autoServers.sorted { server1, server2 in
            // Prioritize online servers
            if server1.status == .online && server2.status != .online {
                return true
            }
            if server1.status != .online && server2.status == .online {
                return false
            }

            // If status is the same, sort by latency
            // Treat servers with 0 latency as high latency
            let ping1 = server1.ping > 0 ? server1.ping : 9999
            let ping2 = server2.ping > 0 ? server2.ping : 9999
            return ping1 < ping2
        }

        // Select the best server
        let bestServer = sortedServers.first!

        logger.info("Selected best auto server", metadata: [
            "server_id": bestServer.id,
            "server_name": bestServer.name,
            "status": bestServer.status.rawValue,
            "ping": "\(bestServer.ping)"
        ])

        return bestServer
    }

    /**
     * NAME: selectServer
     *
     * DESCRIPTION:
     *     Manually selects a server by ID.
     *     Matches Go backend selectServer() functionality.
     *
     * PARAMETERS:
     *     serverID - Server ID to select
     *
     * THROWS:
     *     ServerManagerError - If server not found
     */
    public func selectServer(serverID: String) async throws {
        guard let server = servers.first(where: { $0.id == serverID }) else {
            throw ServerManagerError.serverNotFound(serverID)
        }

        currentServer = server
        selectionMode = .manual

        logger.info("Server manually selected", metadata: [
            "server_id": server.id,
            "server_name": server.name,
            "status": server.status.rawValue,
            "ping": "\(server.ping)"
        ])
    }

    /**
     * NAME: setSelectionMode
     *
     * DESCRIPTION:
     *     Sets server selection mode.
     *
     * PARAMETERS:
     *     mode - New selection mode
     */
    public func setSelectionMode(_ mode: ServerSelectionMode) {
        selectionMode = mode

        logger.info("Selection mode changed", metadata: [
            "mode": mode.rawValue
        ])
    }

    /**
     * NAME: getSelectionMode
     *
     * DESCRIPTION:
     *     Returns current selection mode.
     *
     * RETURNS:
     *     ServerSelectionMode - Current mode
     */
    public func getSelectionMode() -> ServerSelectionMode {
        return selectionMode
    }

    // MARK: - Ping Operations

    /**
     * NAME: pingAllServers
     *
     * DESCRIPTION:
     *     Pings all servers and updates their status and latency.
     *     Timeout servers are reported with latency 0 (unreachable).
     *     This method never throws exceptions.
     */
    public func pingAllServers() async {
        guard !servers.isEmpty else {
            logger.info("No servers to ping, returning early")
            print("🔍 [PING_DEBUG] ServerManager.pingAllServers() - No servers to ping")
            return
        }

        print("🔍 [PING_DEBUG] ServerManager.pingAllServers() - START - server count: \(servers.count)")

        // Log first few servers for debugging
        for (index, server) in servers.prefix(3).enumerated() {
            print("🔍 [PING_DEBUG] Server[\(index)] to ping: \(server.name) - \(server.serverName):\(server.serverPort)")
        }

        let _ = Date() // startTime not used
        var completedPings = 0

        // Use overall timeout for parallel ping operations (5 seconds)
        let overallTimeout = configuration.pingTimeout
        let totalServers = servers.count
        print("🔍 [PING_DEBUG] Using overall timeout: \(overallTimeout)s for \(totalServers) servers")

        // Ping servers in parallel with robust timeout protection
        print("🔍 [PING_DEBUG] Creating TaskGroup for parallel ping operations")
        var allResults: [(String, PingResult)] = []

        // Use a more flexible approach that collects partial results even on timeout
        await withTaskGroup(of: (String, PingResult).self) { group in
            print("🔍 [PING_DEBUG] TaskGroup created, adding ping tasks for \(self.servers.count) servers")

            // Add ping tasks for all servers
            for server in self.servers {
                group.addTask { [weak self] in
                    guard let self = self else {
                        print("🔍 [PING_DEBUG] TaskGroup task - self is nil for server: \(server.id)")
                        return (server.id, PingResult(serverID: server.id, latency: 0, isSuccess: false))
                    }

                    print("🔍 [PING_DEBUG] TaskGroup task - starting ping for server: \(server.id) (\(server.name))")
                    let taskStartTime = Date()
                    let result = await self.pingServerSafe(server)
                    let taskDuration = Date().timeIntervalSince(taskStartTime) * 1000
                    print("🔍 [PING_DEBUG] TaskGroup task - ping completed for server: \(server.id) (\(server.name)), latency: \(result.latency)ms, task_duration: \(taskDuration)ms")
                    return (server.id, result)
                }
            }

            print("🔍 [PING_DEBUG] All \(self.servers.count) ping tasks added, collecting results with timeout")

            // Collect results with overall timeout, but keep partial results
            let collectionStartTime = Date()
            var completedTasks = 0
            let expectedTasks = self.servers.count

            // Use next() to collect results one by one with timeout check
            while completedTasks < expectedTasks {
                let elapsedTime = Date().timeIntervalSince(collectionStartTime)

                // Check if we've exceeded the overall timeout
                if elapsedTime >= overallTimeout {
                    print("🔍 [PING_DEBUG] Overall timeout (\(overallTimeout)s) reached, stopping collection with \(completedTasks)/\(expectedTasks) results")
                    break
                }

                // Try to get next result with a short timeout
                if let result = await group.next() {
                    allResults.append(result)
                    completedTasks += 1
                    print("🔍 [PING_DEBUG] Collected result [\(completedTasks)/\(expectedTasks)] for server: \(result.0) (\(self.servers.first(where: { $0.id == result.0 })?.name ?? "unknown"))")
                } else {
                    // No more results available
                    print("🔍 [PING_DEBUG] No more results available, breaking collection loop")
                    break
                }
            }

            print("🔍 [PING_DEBUG] Result collection completed - collected: \(allResults.count)/\(expectedTasks) in \(Date().timeIntervalSince(collectionStartTime))s")

            // Cancel any remaining tasks
            if completedTasks < expectedTasks {
                print("🔍 [PING_DEBUG] Cancelling \(expectedTasks - completedTasks) remaining tasks")
                group.cancelAll()
            }
        }

        // Process collected results
        print("🔍 [PING_DEBUG] Processing \(allResults.count) collected results")
        let completedServerIDs = Set(allResults.map { $0.0 })

        for (serverID, pingResult) in allResults {
            // Store ping result
            pingResults[serverID] = pingResult

            // Update server status and ping
            if let index = servers.firstIndex(where: { $0.id == serverID }) {
                let oldStatus = servers[index].status
                servers[index].ping = pingResult.latency
                servers[index].status = pingResult.isSuccess ? .online : .offline
                servers[index].lastCheck = pingResult.timestamp

                if oldStatus != servers[index].status {
                    logger.info("Server status changed: \(servers[index].name) \(oldStatus) -> \(servers[index].status)")
                }

                // Notify status change callbacks
                let callbacks = statusCallbacks
                Task {
                    for callback in callbacks {
                        callback(serverID, servers[index].status)
                    }
                }
            }

            completedPings += 1
            // logger.info("Processed result [\(completedPings)/\(totalServers)]: \(serverID) -> \(pingResult.latency)ms (success: \(pingResult.isSuccess))") // Debug log commented for production
        }

        // Handle servers that didn't complete (mark as offline)
        for server in servers where !completedServerIDs.contains(server.id) {
            // Create failed result
            let failedResult = PingResult(serverID: server.id, latency: 0, isSuccess: false)
            pingResults[server.id] = failedResult

            if let index = servers.firstIndex(where: { $0.id == server.id }) {
                let oldStatus = servers[index].status
                servers[index].ping = 0
                servers[index].status = .offline
                servers[index].lastCheck = Date()

                if oldStatus != servers[index].status {
                    logger.info("Server status changed (timeout): \(servers[index].name) \(oldStatus) -> \(servers[index].status)")
                }

                // Notify status change callbacks
                let callbacks = statusCallbacks
                Task {
                    for callback in callbacks {
                        callback(server.id, servers[index].status)
                    }
                }
            }

            completedPings += 1
            logger.info("Marked timeout server [\(completedPings)/\(totalServers)]: \(server.id) -> offline")
        }

        // let duration = Date().timeIntervalSince(startTime)
        // let onlineServers = servers.filter { $0.status.isAvailable }.count

        // logger.info("Ping operation completed", metadata: [
        //     "online_servers": "\(onlineServers)",
        //     "total_servers": "\(servers.count)",
        //     "completed_pings": "\(completedPings)",
        //     "duration": String(format: "%.2f", duration)
        // ]) // Debug log commented for production
    }

    /**
     * NAME: pingServerSafe
     *
     * DESCRIPTION:
     *     Safely pings a single server with improved error handling and resource management.
     *     Uses 3-packet concurrent testing for improved accuracy - sends 3 packets simultaneously
     *     and returns the lowest latency from successful responses.
     *     This method never throws exceptions and always returns a valid PingResult.
     *
     * PARAMETERS:
     *     server - Server to ping
     *
     * RETURNS:
     *     PingResult - Ping operation result (never throws)
     */
    private func pingServerSafe(_ server: ServerInfo) async -> PingResult {
        let pingStartTime = Date()
        print("🔍 [PING_DEBUG] pingServerSafe() - START for server: \(server.name) (ID: \(server.id), Address: \(server.serverName):\(server.serverPort)) at \(pingStartTime.timeIntervalSince1970)")

        // Always collect results, even if some operations fail or timeout
        var collectedResults: [(Int, Bool)] = []

        do {
            print("🔍 [PING_DEBUG] pingServerSafe() - starting ping operation for server: \(server.name)")

            // Resolve server IP using cache first, fallback to hostname
            print("🔍 [PING_DEBUG] pingServerSafe() - starting DNS resolution for \(server.serverName)")
            let dnsStartTime = Date()
            let serverAddress = await resolveServerIP(server.serverName) ?? server.serverName
            let dnsTime = Date().timeIntervalSince(dnsStartTime) * 1000
            print("🔍 [PING_DEBUG] pingServerSafe() - DNS resolution completed in \(dnsTime)ms: \(server.serverName) -> \(serverAddress)")

            // Create ping packet once for all attempts
            let pingPacket = try createPingPacket()
            print("🔍 [PING_DEBUG] pingServerSafe() - created ping packet for \(server.name)")

            // Execute 3 concurrent ping attempts (like Android's async + awaitAll)
            print("🔍 [PING_DEBUG] pingServerSafe() - starting 3 concurrent ping attempts for \(server.name)")
            collectedResults = await withTaskGroup(of: (Int, Bool).self, returning: [(Int, Bool)].self) { group in
                // Add 3 ping tasks
                for packetIndex in 1...3 {
                    group.addTask { [weak self] in
                        guard let self = self else {
                            print("🔍 [PING_DEBUG] pingServerSafe() - self is nil for packet \(packetIndex)")
                            return (0, false)
                        }
                        print("🔍 [PING_DEBUG] pingServerSafe() - starting packet \(packetIndex) for \(server.name)")
                        let packetStartTime = Date()
                        let result = await self.performSinglePingAttempt(
                            serverAddress: serverAddress,
                            port: server.serverPort,
                            packet: pingPacket,
                            packetIndex: packetIndex,
                            serverName: server.name
                        )
                        let packetDuration = Date().timeIntervalSince(packetStartTime) * 1000
                        print("🔍 [PING_DEBUG] pingServerSafe() - packet \(packetIndex) completed for \(server.name): \(result.0)ms, success: \(result.1), duration: \(packetDuration)ms")
                        return result
                    }
                }

                // Collect all results with timeout protection
                var allResults: [(Int, Bool)] = []
                var completedPackets = 0
                let expectedPackets = 3

                print("🔍 [PING_DEBUG] pingServerSafe() - collecting results for \(server.name)")
                while completedPackets < expectedPackets {
                    if let result = await group.next() {
                        allResults.append(result)
                        completedPackets += 1
                        print("🔍 [PING_DEBUG] pingServerSafe() - collected result [\(completedPackets)/\(expectedPackets)] for \(server.name)")
                    } else {
                        print("🔍 [PING_DEBUG] pingServerSafe() - no more results available for \(server.name), breaking")
                        break
                    }
                }

                if completedPackets < expectedPackets {
                    print("🔍 [PING_DEBUG] pingServerSafe() - cancelling \(expectedPackets - completedPackets) remaining packets for \(server.name)")
                    group.cancelAll()
                }

                print("🔍 [PING_DEBUG] pingServerSafe() - all \(completedPackets) results collected for \(server.name)")
                return allResults
            }

        } catch {
            let totalDuration = Date().timeIntervalSince(pingStartTime) * 1000
            print("🔍 [PING_DEBUG] pingServerSafe() - EXCEPTION during ping setup for \(server.name) after \(totalDuration)ms: \(error.localizedDescription)")
            print("🔍 [PING_DEBUG] pingServerSafe() - Error type: \(type(of: error))")
            if let serverError = error as? ServerManagerError {
                print("🔍 [PING_DEBUG] pingServerSafe() - ServerManagerError: \(serverError)")
            }
            // Even if setup failed, we might have collected some results, so continue processing
        }

        // Process collected results: filter successful pings and find minimum latency
        let successfulLatencies = collectedResults.compactMap { latency, success in
            success && latency > 0 ? latency : nil
        }
        print("🔍 [PING_DEBUG] pingServerSafe() - processing \(collectedResults.count) collected results for \(server.name): successful packets: \(successfulLatencies.count)")

        if let minLatency = successfulLatencies.min() {
            print("🔍 [PING_DEBUG] pingServerSafe() - SUCCESS for \(server.name): min latency \(minLatency)ms from \(successfulLatencies.count) successful packets")
            return PingResult(
                serverID: server.id,
                latency: minLatency,
                isSuccess: true
            )
        } else {
            print("🔍 [PING_DEBUG] pingServerSafe() - FAILED for \(server.name): no successful packets from \(collectedResults.count) total results")
            return PingResult(
                serverID: server.id,
                latency: 0,
                isSuccess: false
            )
        }
    }

    /**
     * NAME: performSinglePingAttempt
     *
     * DESCRIPTION:
     *     Performs a single ping attempt as part of the 3-packet concurrent test.
     *     Creates an independent connection and measures round-trip time.
     *
     * PARAMETERS:
     *     serverAddress - Target server address (IP or hostname)
     *     port - Target server port
     *     packet - Pre-created ping packet data
     *     packetIndex - Index of this packet (1-3) for logging
     *     serverName - Server name for logging
     *
     * RETURNS:
     *     (Int, Bool) - Tuple of (latency in ms, success flag)
     */
    private func performSinglePingAttempt(
        serverAddress: String,
        port: Int,
        packet: Data,
        packetIndex: Int,
        serverName: String
    ) async -> (Int, Bool) {
        print("🔍 [PING_DEBUG] performSinglePingAttempt() - START packet \(packetIndex) for \(serverName)")
        var connection: NWConnection?

        do {
            // Create independent UDP connection for this attempt
            print("🔍 [PING_DEBUG] performSinglePingAttempt() - creating UDP connection for packet \(packetIndex)")
            connection = try await createUDPConnection(
                host: serverAddress,
                port: port
            )

            guard let conn = connection else {
                logger.debug("Failed to create connection for packet \(packetIndex)", metadata: [
                    "server": serverName,
                    "packet": "\(packetIndex)"
                ])
                print("🔍 [PING_DEBUG] performSinglePingAttempt() - FAILED to create connection for packet \(packetIndex)")
                return (0, false)
            }
            print("🔍 [PING_DEBUG] performSinglePingAttempt() - UDP connection created for packet \(packetIndex)")

            // Ensure connection cleanup
            defer {
                Task {
                    try? await Task.sleep(nanoseconds: 10_000_000) // 10ms
                    conn.cancel()
                }
            }

            // Send ping packet first
            print("🔍 [PING_DEBUG] performSinglePingAttempt() - sending ping packet \(packetIndex)")
            try await sendPingPacket(connection: conn, packet: packet)
            print("🔍 [PING_DEBUG] performSinglePingAttempt() - ping packet \(packetIndex) sent successfully")

            // Record start time immediately after packet is sent (actual network measurement start)
            let startTime = Date()

            // Wait for response with safe timeout handling
            print("🔍 [PING_DEBUG] performSinglePingAttempt() - waiting for response packet \(packetIndex)")
            let success: Bool
            do {
                success = try await waitForPingResponse(
                    connection: conn,
                    timeout: configuration.pingTimeout
                )
            } catch {
                // Handle timeout gracefully - don't let it propagate as exception
                print("🔍 [PING_DEBUG] performSinglePingAttempt() - timeout/error for packet \(packetIndex): \(error.localizedDescription)")
                success = false
            }
            print("🔍 [PING_DEBUG] performSinglePingAttempt() - response wait completed for packet \(packetIndex), success: \(success)")

            // Calculate latency
            let latency = Int(Date().timeIntervalSince(startTime) * 1000)

            if success {
                logger.debug("Packet \(packetIndex) successful", metadata: [
                    "server": serverName,
                    "packet": "\(packetIndex)",
                    "latency": "\(latency)ms"
                ])
                print("🔍 [PING_DEBUG] performSinglePingAttempt() - packet \(packetIndex) SUCCESS: \(latency)ms")
            } else {
                logger.debug("Packet \(packetIndex) failed - no response", metadata: [
                    "server": serverName,
                    "packet": "\(packetIndex)"
                ])
                print("🔍 [PING_DEBUG] performSinglePingAttempt() - packet \(packetIndex) FAILED: no response")
            }

            print("🔍 [PING_DEBUG] performSinglePingAttempt() - RETURNING result for packet \(packetIndex): (\(success ? latency : 0), \(success))")
            return (success ? latency : 0, success)

        } catch {
            logger.debug("Packet \(packetIndex) failed with error", metadata: [
                "server": serverName,
                "packet": "\(packetIndex)",
                "error": error.localizedDescription
            ])
            print("🔍 [PING_DEBUG] performSinglePingAttempt() - EXCEPTION for packet \(packetIndex): \(error.localizedDescription)")

            // Cleanup connection on error
            if let conn = connection {
                Task {
                    try? await Task.sleep(nanoseconds: 10_000_000) // 10ms
                    conn.cancel()
                }
            }

            return (0, false)
        }
    }

    /**
     * NAME: pingServer
     *
     * DESCRIPTION:
     *     Pings a single server and returns result.
     *
     * PARAMETERS:
     *     server - Server to ping
     *
     * RETURNS:
     *     PingResult - Ping operation result
     */
    private func pingServer(_ server: ServerInfo) async -> PingResult {
        // logger.info("Starting ping for server: \(server.name) (\(server.serverName):\(server.serverPort))") // Debug log commented for production

        do {
            // Resolve server IP using cache first, fallback to hostname
            let serverAddress = await resolveServerIP(server.serverName) ?? server.serverName
            let useResolvedIP = serverAddress != server.serverName

            logger.debug("Using server address for ping", metadata: [
                "hostname": server.serverName,
                "resolved_address": serverAddress,
                "from_cache": serverIPCache.keys.contains(server.serverName) ? "true" : "false",
                "use_resolved_ip": useResolvedIP ? "true" : "false",
                "fallback_note": useResolvedIP ? "Using resolved IP" : "Using hostname fallback - DNS resolution will occur in network layer"
            ])

            // Create UDP connection
            // logger.info("Creating UDP connection to \(serverAddress):\(server.serverPort)") // Debug log commented for production
            let connection = try await createUDPConnection(
                host: serverAddress,
                port: server.serverPort
            )
            // logger.info("UDP connection created successfully for \(server.name)") // Debug log commented for production

            defer {
                // logger.info("Cancelling UDP connection for \(server.name)") // Debug log commented for production
                connection.cancel()
            }

            // Create ping packet (OPEN packet with dummy credentials)
            // logger.info("Creating ping packet for \(server.name)") // Debug log commented for production
            let pingPacket = try createPingPacket()
            // logger.info("Ping packet created, size: \(pingPacket.count) bytes") // Debug log commented for production

            // Send ping packet
            // logger.info("Sending ping packet to \(server.name)") // Debug log commented for production
            try await sendPingPacket(connection: connection, packet: pingPacket)
            // logger.info("Ping packet sent to \(server.name)") // Debug log commented for production

            // Record time immediately after packet is sent (actual network measurement start)
            let networkStartTime = Date()

            // Wait for response or timeout
            // logger.info("Waiting for ping response from \(server.name) (timeout: \(configuration.pingTimeout)s)") // Debug log commented for production
            let success = try await waitForPingResponse(
                connection: connection,
                timeout: configuration.pingTimeout
            )
            // logger.info("Ping response received from \(server.name): success=\(success)") // Debug log commented for production

            // Calculate actual network round-trip time (excluding setup overhead)
            let latency = Int(Date().timeIntervalSince(networkStartTime) * 1000)
            // logger.info("Ping completed for \(server.name): \(latency)ms") // Debug log commented for production

            let result = PingResult(
                serverID: server.id,
                latency: success ? latency : 0,
                isSuccess: success
            )
            // logger.info("Returning ping result for \(server.name): success=\(result.isSuccess), latency=\(result.latency)ms") // Debug log commented for production
            return result

        } catch {
            logger.error("Server ping failed for \(server.name)", metadata: [
                "server": server.name,
                "address": server.serverName,
                "error": error.localizedDescription,
                "error_type": "\(type(of: error))"
            ])

            let result = PingResult(
                serverID: server.id,
                latency: 0,
                isSuccess: false
            )
            // logger.info("Returning failed ping result for \(server.name)") // Debug log commented for production
            return result
        }
    }

    /**
     * NAME: getPingResults
     *
     * DESCRIPTION:
     *     Returns ping results for all servers.
     *
     * RETURNS:
     *     [String: PingResult] - Ping results by server ID
     */
    public func getPingResults() -> [String: PingResult] {
        return pingResults
    }

    /**
     * NAME: getPingTimeout
     *
     * DESCRIPTION:
     *     Returns the ping timeout configuration.
     *
     * RETURNS:
     *     TimeInterval - Ping timeout in seconds
     */
    public func getPingTimeout() -> TimeInterval {
        return configuration.pingTimeout
    }

    // MARK: - Callback Management

    /**
     * NAME: registerUpdateCallback
     *
     * DESCRIPTION:
     *     Registers callback for server list updates.
     *
     * PARAMETERS:
     *     callback - Update callback function
     */
    public func registerUpdateCallback(_ callback: @escaping ServerUpdateCallback) {
        updateCallbacks.append(callback)
        // logger.debug("Server update callback registered", metadata: [
        //     "total_callbacks": "\(updateCallbacks.count)"
        // ]) // Debug log commented for production
    }

    /**
     * NAME: registerStatusCallback
     *
     * DESCRIPTION:
     *     Registers callback for server status changes.
     *
     * PARAMETERS:
     *     callback - Status callback function
     */
    public func registerStatusCallback(_ callback: @escaping ServerStatusCallback) {
        statusCallbacks.append(callback)
        // logger.debug("Server status callback registered", metadata: [
        //     "total_callbacks": "\(statusCallbacks.count)"
        // ]) // Debug log commented for production
    }



    /**
     * NAME: clearCallbacks
     *
     * DESCRIPTION:
     *     Clears all registered callbacks.
     */
    public func clearCallbacks() {
        updateCallbacks.removeAll()
        statusCallbacks.removeAll()
        // logger.debug("All callbacks cleared") // Debug log commented for production
    }

    // MARK: - Statistics and Status

    /**
     * NAME: getStatistics
     *
     * DESCRIPTION:
     *     Returns server manager statistics.
     *
     * RETURNS:
     *     [String: Any] - Statistics dictionary
     */
    public func getStatistics() -> [String: Any] {
        let onlineServers = servers.filter { $0.status.isAvailable }
        let autoServers = servers.filter { $0.isAuto }

        return [
            "total_servers": servers.count,
            "online_servers": onlineServers.count,
            "offline_servers": servers.count - onlineServers.count,
            "auto_servers": autoServers.count,
            "current_server_id": currentServer?.id ?? "",
            "selection_mode": selectionMode.rawValue,
            "is_running": isRunning,
            "ping_results_count": pingResults.count,
            "callback_counts": [
                "update_callbacks": updateCallbacks.count,
                "status_callbacks": statusCallbacks.count
            ]
        ]
    }

    /**
     * NAME: reset
     *
     * DESCRIPTION:
     *     Resets server manager to initial state.
     */
    public func reset() async {
        await stop()

        servers.removeAll()
        currentServer = nil
        pingResults.removeAll()
        selectionMode = .auto

        logger.info("Server manager reset to initial state")
    }

    // MARK: - Private Helper Methods

    /**
     * NAME: withTimeout
     *
     * DESCRIPTION:
     *     Executes an async operation with timeout protection.
     *
     * PARAMETERS:
     *     seconds - Timeout duration in seconds
     *     operation - The async operation to execute
     *
     * RETURNS:
     *     T - Result of the operation
     *
     * THROWS:
     *     TimeoutError - If operation times out
     */
    private func withTimeout<T>(seconds: TimeInterval, operation: @escaping () async throws -> T) async throws -> T {
        let timeoutStartTime = Date()
        print("🔍 [TIMEOUT_DEBUG] withTimeout(\(seconds)s) - START at \(timeoutStartTime.timeIntervalSince1970)")

        // Use iOS standard Task timeout pattern with proper error handling
        return try await withThrowingTaskGroup(of: T.self) { group in
            // Add the main operation
            group.addTask {
                print("🔍 [TIMEOUT_DEBUG] withTimeout - main operation task started")
                let result = try await operation()
                print("🔍 [TIMEOUT_DEBUG] withTimeout - main operation completed successfully")
                return result
            }

            // Add timeout task
            group.addTask {
                print("🔍 [TIMEOUT_DEBUG] withTimeout - timeout task started, will timeout in \(seconds)s")
                try await Task.sleep(nanoseconds: UInt64(seconds * 1_000_000_000))
                print("🔍 [TIMEOUT_DEBUG] withTimeout - TIMEOUT REACHED after \(seconds)s")
                throw ServerManagerError.timeout
            }

            // Wait for first result and cancel remaining tasks
            print("🔍 [TIMEOUT_DEBUG] withTimeout - waiting for first result...")
            do {
                for try await result in group {
                    let elapsedTime = Date().timeIntervalSince(timeoutStartTime)
                    print("🔍 [TIMEOUT_DEBUG] withTimeout - got result after \(elapsedTime)s, cancelling remaining tasks")
                    group.cancelAll()
                    return result
                }
            } catch {
                let elapsedTime = Date().timeIntervalSince(timeoutStartTime)
                print("🔍 [TIMEOUT_DEBUG] withTimeout - ERROR after \(elapsedTime)s: \(error)")
                group.cancelAll()
                throw error
            }

            // Fallback (should not reach here)
            print("🔍 [TIMEOUT_DEBUG] withTimeout - FALLBACK: no results received")
            throw ServerManagerError.timeout
        }
    }



    /**
     * NAME: startUpdateTask
     *
     * DESCRIPTION:
     *     Starts background server list update task.
     */
    private func startUpdateTask() {
        updateTask = Task { [weak self] in
            while !Task.isCancelled {
                guard let self = self, await self.isRunning else {
                    break
                }

                // Perform periodic server list updates
                // This would typically fetch from remote sources
                // await self.logger.debug("Server list update task running") // Debug log commented for production

                try? await Task.sleep(nanoseconds: UInt64(self.configuration.updateInterval * 1_000_000_000))
            }
        }
    }

    // Note: Background ping task moved to ServerService to ensure proper delegate notifications



    // MARK: - Ping Helper Methods

    /**
     * NAME: createUDPConnection
     *
     * DESCRIPTION:
     *     Creates UDP connection to server.
     *
     * PARAMETERS:
     *     host - Server hostname or IP
     *     port - Server port
     *
     * RETURNS:
     *     NWConnection - UDP connection
     *
     * THROWS:
     *     ServerManagerError - If connection creation fails
     */
    private func createUDPConnection(host: String, port: Int) async throws -> NWConnection {
        let endpoint = NWEndpoint.hostPort(
            host: NWEndpoint.Host(host),
            port: NWEndpoint.Port(integerLiteral: UInt16(port))
        )

        let connection = NWConnection(to: endpoint, using: .udp)

        return try await withCheckedThrowingContinuation { continuation in
            // Use a class to safely track resume state in concurrent context
            final class ResumeTracker: @unchecked Sendable {
                private let lock = NSLock()
                private var _isResumed = false

                var isResumed: Bool {
                    lock.lock()
                    defer { lock.unlock() }
                    return _isResumed
                }

                func markResumed() -> Bool {
                    lock.lock()
                    defer { lock.unlock() }
                    if _isResumed {
                        return false // Already resumed
                    }
                    _isResumed = true
                    return true // Successfully marked as resumed
                }
            }

            let tracker = ResumeTracker()

            connection.stateUpdateHandler = { state in
                switch state {
                case .ready:
                    if tracker.markResumed() {
                        continuation.resume(returning: connection)
                    }
                case .failed(let error):
                    if tracker.markResumed() {
                        continuation.resume(throwing: ServerManagerError.pingFailed(error.localizedDescription))
                    }
                case .cancelled:
                    if tracker.markResumed() {
                        continuation.resume(throwing: ServerManagerError.pingFailed("Connection cancelled"))
                    }
                default:
                    break
                }
            }

            connection.start(queue: .global())
        }
    }

    /**
     * NAME: createPingPacket
     *
     * DESCRIPTION:
     *     Creates ping packet (OPEN packet with dummy credentials).
     *
     * RETURNS:
     *     Data - Ping packet data
     *
     * THROWS:
     *     ServerManagerError - If packet creation fails
     */
    private func createPingPacket() throws -> Data {
        // Create complete OPEN packet for ping to match Go backend implementation
        // Go backend uses: tunnel.CreateOpenPacket("notExist", "mPassword", 1420, 0)
        // logger.info("Creating OPEN packet for ping with dummy credentials") // Debug log commented for production

        do {
            let packetBuilder = PacketBuilder()
            let pingPacket = try packetBuilder.buildOpenPacket(
                username: "notExist",      // Same as Go backend
                password: "mPassword",     // Same as Go backend
                mtu: 1420,                 // Same as Go backend
                encryptionMethod: .none    // Same as Go backend (0)
            )

            // logger.info("OPEN ping packet created successfully, size: \(pingPacket.count) bytes") // Debug log commented for production
            return pingPacket
        } catch {
            logger.error("Failed to create OPEN ping packet: \(error.localizedDescription)")
            throw ServerManagerError.pingFailed("Failed to create ping packet: \(error.localizedDescription)")
        }
    }

    /**
     * NAME: sendPingPacket
     *
     * DESCRIPTION:
     *     Sends ping packet through connection.
     *
     * PARAMETERS:
     *     connection - UDP connection
     *     packet - Ping packet data
     *
     * THROWS:
     *     ServerManagerError - If send operation fails
     */
    private func sendPingPacket(connection: NWConnection, packet: Data) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            connection.send(content: packet, completion: .contentProcessed { error in
                if let error = error {
                    continuation.resume(throwing: ServerManagerError.pingFailed(error.localizedDescription))
                } else {
                    continuation.resume()
                }
            })
        }
    }

    /**
     * NAME: waitForPingResponseSafe
     *
     * DESCRIPTION:
     *     Safely waits for ping response with improved timeout handling and resource management.
     *
     * PARAMETERS:
     *     connection - UDP connection
     *     timeout - Timeout duration
     *
     * RETURNS:
     *     Bool - True if response received, false if timeout
     *
     * THROWS:
     *     ServerManagerError - If operation fails
     */
    private func waitForPingResponseSafe(connection: NWConnection, timeout: TimeInterval) async throws -> Bool {
        print("🔍 [PING_DEBUG] waitForPingResponseSafe() - START - timeout: \(timeout)s")

        // Use withTimeout to ensure the entire operation completes within timeout
        do {
            return try await withTimeout(seconds: timeout) {
                return try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Bool, Error>) in
                    var resumed = false
                    print("🔍 [PING_DEBUG] waitForPingResponseSafe() - Setting up receive callback")

                    connection.receive(minimumIncompleteLength: 1, maximumLength: 1024) { data, _, isComplete, error in
                        guard !resumed else {
                            print("🔍 [PING_DEBUG] waitForPingResponseSafe() - Receive callback called but already resumed")
                            return
                        }
                        resumed = true

                        if let error = error {
                            print("🔍 [PING_DEBUG] waitForPingResponseSafe() - Receive error: \(error.localizedDescription)")
                            continuation.resume(returning: false)
                        } else if data != nil {
                            print("🔍 [PING_DEBUG] waitForPingResponseSafe() - Data received successfully")
                            continuation.resume(returning: true)
                        } else if isComplete {
                            print("🔍 [PING_DEBUG] waitForPingResponseSafe() - Connection completed without data")
                            continuation.resume(returning: false)
                        }
                    }

                    print("🔍 [PING_DEBUG] waitForPingResponseSafe() - Receive callback set up, waiting for response or timeout")
                }
            }
        } catch {
            print("🔍 [PING_DEBUG] waitForPingResponseSafe() - Timeout occurred: \(error.localizedDescription)")
            return false
        }
    }

    /**
     * NAME: waitForPingResponse
     *
     * DESCRIPTION:
     *     Waits for ping response or timeout.
     *
     * PARAMETERS:
     *     connection - UDP connection
     *     timeout - Timeout duration
     *
     * RETURNS:
     *     Bool - True if response received, false if timeout
     *
     * THROWS:
     *     ServerManagerError - If operation fails
     */
    private func waitForPingResponse(connection: NWConnection, timeout: TimeInterval) async throws -> Bool {
        print("🔍 [PING_DEBUG] waitForPingResponse() - START waiting for response, timeout: \(timeout)s")

        // Use a more robust timeout mechanism that forces completion
        return try await withTimeout(seconds: timeout) {
            return try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Bool, Error>) in
                // Use a class to safely track resume state in concurrent context
                final class ResumeTracker: @unchecked Sendable {
                    private let lock = NSLock()
                    private var _isResumed = false

                    var isResumed: Bool {
                        lock.lock()
                        defer { lock.unlock() }
                        return _isResumed
                    }

                    func markResumed() -> Bool {
                        lock.lock()
                        defer { lock.unlock() }
                        if _isResumed {
                            return false
                        }
                        _isResumed = true
                        return true
                    }
                }

                let resumeTracker = ResumeTracker()
                print("🔍 [PING_DEBUG] waitForPingResponse() - calling connection.receive()")

                connection.receive(minimumIncompleteLength: 1, maximumLength: 1024) { data, _, isComplete, error in
                    print("🔍 [PING_DEBUG] waitForPingResponse() - receive callback called: data=\(data?.count ?? 0) bytes, isComplete=\(isComplete), error=\(error?.localizedDescription ?? "nil")")

                    guard resumeTracker.markResumed() else {
                        print("🔍 [PING_DEBUG] waitForPingResponse() - callback called but already resumed")
                        return
                    }

                    if let error = error {
                        print("🔍 [PING_DEBUG] waitForPingResponse() - receive error: \(error.localizedDescription)")
                        continuation.resume(throwing: ServerManagerError.pingFailed(error.localizedDescription))
                    } else if let data = data, !data.isEmpty {
                        print("🔍 [PING_DEBUG] waitForPingResponse() - received data: \(data.count) bytes")
                        continuation.resume(returning: true)
                    } else if isComplete {
                        print("🔍 [PING_DEBUG] waitForPingResponse() - connection completed without data")
                        continuation.resume(returning: false)
                    } else {
                        print("🔍 [PING_DEBUG] waitForPingResponse() - no data and not complete, but resuming with false to avoid hanging")
                        continuation.resume(returning: false)
                    }
                }

                // Add a safety timeout that will force resume if receive callback never gets called
                Task {
                    try? await Task.sleep(nanoseconds: UInt64((timeout - 0.1) * 1_000_000_000)) // Slightly less than outer timeout
                    if resumeTracker.markResumed() {
                        print("🔍 [PING_DEBUG] waitForPingResponse() - SAFETY TIMEOUT: forcing resume after \(timeout - 0.1)s")
                        continuation.resume(returning: false)
                    }
                }
            }
        }
    }

    // MARK: - IP Cache Management Methods

    /**
     * NAME: getCachedServerIP
     *
     * DESCRIPTION:
     *     Gets cached IP address for a hostname.
     *     Returns cached IP if available, nil otherwise.
     *
     * PARAMETERS:
     *     hostname - Server hostname to lookup
     *
     * RETURNS:
     *     String? - Cached IP address or nil if not cached
     */
    public func getCachedServerIP(_ hostname: String) -> String? {
        return serverIPCache[hostname]
    }

    /**
     * NAME: resolveServerIP
     *
     * DESCRIPTION:
     *     Resolves server hostname to IP address with caching.
     *     Uses cache first, performs DNS resolution if needed.
     *
     * PARAMETERS:
     *     hostname - Server hostname to resolve
     *
     * RETURNS:
     *     String? - Resolved IP address or nil if failed
     */
    public func resolveServerIP(_ hostname: String) async -> String? {
        // Check cache first
        if let cachedIP = serverIPCache[hostname] {
            return cachedIP
        }

        // Resolve and cache
        if let resolvedIP = await resolveHostnameToIP(hostname) {
            serverIPCache[hostname] = resolvedIP

            // Store in App Group for VPN extension access
            storeServerIPInAppGroup(hostname, resolvedIP)

            return resolvedIP
        }

        return nil
    }

    /**
     * NAME: getAllCachedServerIPs
     *
     * DESCRIPTION:
     *     Gets all cached server IP addresses for route exclusion.
     *     Used by ConnectionManager to exclude server IPs from VPN routing.
     *
     * RETURNS:
     *     [String] - List of all cached server IP addresses
     */
    public func getAllCachedServerIPs() -> [String] {
        return Array(serverIPCache.values)
    }

    /**
     * NAME: clearIPCache
     *
     * DESCRIPTION:
     *     Clears all cached IP addresses.
     *     Useful for testing or when network configuration changes.
     */
    public func clearIPCache() {
        serverIPCache.removeAll()
        clearServerIPCacheInAppGroup()
        logger.info("IP cache cleared")
    }

    // MARK: - App Group IP Cache Methods

    /**
     * NAME: storeServerIPInAppGroup
     *
     * DESCRIPTION:
     *     Stores server IP in App Group for VPN extension access.
     *     This allows VPN extension to use cached IPs from main app.
     *
     * PARAMETERS:
     *     hostname - Server hostname
     *     ip - Resolved IP address
     */
    private func storeServerIPInAppGroup(_ hostname: String, _ ip: String) {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.warning("Failed to access App Group UserDefaults for storing server IP cache")
            return
        }

        var serverIPCache = userDefaults.object(forKey: "server_ip_cache") as? [String: Any] ?? [:]

        // Store IP and metadata
        serverIPCache[hostname] = ip
        serverIPCache["\(hostname)_metadata"] = [
            "timestamp": Date().timeIntervalSince1970,
            "source": "server_manager"
        ]

        userDefaults.set(serverIPCache, forKey: "server_ip_cache")
        userDefaults.synchronize()

        logger.debug("Stored server IP in App Group cache", metadata: [
            "hostname": hostname,
            "ip": ip
        ])
    }

    /**
     * NAME: clearServerIPCacheInAppGroup
     *
     * DESCRIPTION:
     *     Clears server IP cache in App Group.
     */
    private func clearServerIPCacheInAppGroup() {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else {
            logger.warning("Failed to access App Group UserDefaults for clearing server IP cache")
            return
        }

        userDefaults.removeObject(forKey: "server_ip_cache")
        userDefaults.synchronize()

        logger.debug("Cleared server IP cache in App Group")
    }

    /**
     * NAME: resolveHostnameToIP
     *
     * DESCRIPTION:
     *     Resolves hostname to IP address using system DNS.
     *     This is a private helper method for DNS resolution.
     *
     * PARAMETERS:
     *     hostname - Hostname to resolve
     *
     * RETURNS:
     *     String? - Resolved IP address or nil if failed
     */
    private func resolveHostnameToIP(_ hostname: String) async -> String? {
        // Check if hostname is already an IP address
        if isValidIPAddress(hostname) {
            return hostname
        }

        return await withCheckedContinuation { continuation in
            let host = CFHostCreateWithName(nil, hostname as CFString).takeRetainedValue()

            var result: DarwinBoolean = false
            let addresses = CFHostGetAddressing(host, &result)

            if result.boolValue, let addresses = addresses?.takeUnretainedValue() as? [Data] {
                for addressData in addresses {
                    var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
                    let result = addressData.withUnsafeBytes { bytes in
                        getnameinfo(
                            bytes.bindMemory(to: sockaddr.self).baseAddress,
                            socklen_t(addressData.count),
                            &hostname,
                            socklen_t(hostname.count),
                            nil,
                            0,
                            NI_NUMERICHOST
                        )
                    }

                    if result == 0 {
                        let ipString = String(cString: hostname)
                        continuation.resume(returning: ipString)
                        return
                    }
                }
            }

            // If CFHost resolution fails, try using getaddrinfo
            var hints = addrinfo()
            hints.ai_family = AF_UNSPEC
            hints.ai_socktype = SOCK_STREAM

            var result_ptr: UnsafeMutablePointer<addrinfo>?
            let status = getaddrinfo(hostname, nil, &hints, &result_ptr)

            defer {
                if let result_ptr = result_ptr {
                    freeaddrinfo(result_ptr)
                }
            }

            if status == 0, let result_ptr = result_ptr {
                var hostname_cstr = [CChar](repeating: 0, count: Int(NI_MAXHOST))
                let gni_result = getnameinfo(
                    result_ptr.pointee.ai_addr,
                    result_ptr.pointee.ai_addrlen,
                    &hostname_cstr,
                    socklen_t(hostname_cstr.count),
                    nil,
                    0,
                    NI_NUMERICHOST
                )

                if gni_result == 0 {
                    let ipString = String(cString: hostname_cstr)
                    continuation.resume(returning: ipString)
                    return
                }
            }

            logger.warning("Failed to resolve hostname", metadata: [
                "hostname": hostname,
                "error": "DNS resolution failed"
            ])
            continuation.resume(returning: nil)
        }
    }

    /**
     * NAME: isValidIPAddress
     *
     * DESCRIPTION:
     *     Checks if a string is a valid IP address.
     *
     * PARAMETERS:
     *     string - String to check
     *
     * RETURNS:
     *     Bool - True if valid IP address, false otherwise
     */
    private func isValidIPAddress(_ string: String) -> Bool {
        var sin = sockaddr_in()
        var sin6 = sockaddr_in6()

        return string.withCString { cstring in
            return inet_pton(AF_INET, cstring, &sin.sin_addr) == 1 ||
                   inet_pton(AF_INET6, cstring, &sin6.sin6_addr) == 1
        }
    }
}

// MARK: - Supporting Types

// Note: TimeoutError is defined in VPNService.swift and imported globally
