# iOS VPN Extension与主应用通信架构重构方案

## 文档概述

本文档详细描述了iOS VPN Extension与主应用通信架构的重构方案，旨在解决当前架构中功能重复、通信机制不完善、职责边界模糊等问题，建立清晰高效的通信架构。

## 1. 当前架构问题分析

### 1.1 功能重复和资源竞争

**问题表现：**
- **主应用ServerService**：拥有完整的服务器管理功能，包括ping测试、服务器选择、列表更新
- **VPN Extension**：通过ConnectionManager也有服务器管理能力，但功能不完整
- 两者都在独立进行网络操作，造成资源浪费和状态不一致

**具体问题：**
```swift
// 主应用ServerService.swift - 重复的ping功能
public func pingAllServers() async {
    await _serverManager.pingAllServers()
    delegate?.serverService(self, didCompletePing: pingResults)
}

// VPN Extension PacketTunnelProvider.swift - 重复的服务器管理
connectionManager = ConnectionManager(
    serverManager: serverManager,  // 重复的服务器管理实例
    logger: logger
)
```

### 1.2 通信机制不完善

**当前通信方式：**
- 主要依赖App Group UserDefaults进行数据共享
- 缺乏实时事件通知机制
- VPN Extension状态变化无法及时通知主应用

**问题示例：**
```swift
// 只能被动读取，无实时通知
private func getSharedTrafficStatisticsForMonitoring() -> TrafficStatistics {
    guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient"),
          let sharedStats = userDefaults.object(forKey: "vpn_traffic_statistics") as? [String: Any] else {
        return TrafficStatistics()  // 无法获得实时更新
    }
}
```

### 1.3 职责边界模糊

**主应用承担过多职责：**
- 服务器列表管理和更新
- ping测试执行
- 服务器选择逻辑
- 这些应该由VPN Extension处理，因为它有持续后台运行权限

**VPN Extension职责不足：**
- 缺乏完整的服务器管理功能
- 无法主动进行ping测试
- 无法响应主应用的操作请求

## 2. 重构目标

### 2.1 功能职责重新划分

**VPN Extension负责：**
- 网络协议处理：UDP连接、SDWAN协议认证、数据传输
- 服务器管理：获取服务器列表、ping测试、服务器选择
- 连接管理：VPN连接建立、断开、重连、心跳检测
- 状态监控：网络接口监控、流量统计、连接状态维护

**主应用负责：**
- UI展示：连接状态、服务器列表、流量统计显示
- 用户交互：连接/断开操作、服务器选择、设置配置
- 配置管理：服务器列表URL设置、用户偏好保存
- 状态读取：从VPN Extension读取最新状态并更新UI

### 2.2 通信机制优化

**App Group + Darwin Notifications组合方案：**
- **App Group UserDefaults**：用于数据存储和共享
  - 服务器列表、ping结果、连接状态等复杂数据
  - 支持跨进程安全访问
  - 自动持久化
- **Darwin Notifications**：用于实时事件通知
  - 主应用请求操作（如ping）
  - VPN Extension完成通知（如服务器列表更新）
  - 系统级别，可靠性高

### 2.3 架构优势

**性能优化：**
- 消除重复逻辑和资源竞争
- VPN Extension持续运行，避免重复初始化
- 实时通信减少轮询开销

**可靠性提升：**
- 单一数据源，避免状态不一致
- 符合iOS系统设计原则
- 减少进程间复杂交互

**维护性改善：**
- 职责边界清晰
- 代码逻辑简化
- 易于调试和扩展

## 3. 重构实施计划

### 3.1 阶段一：设计Darwin Notifications通信协议

**定义事件类型：**
```swift
// Darwin Notifications事件定义
enum VPNExtensionNotification: String {
    case pingRequest = "com.panabit.vpn.ping.request"
    case pingCompleted = "com.panabit.vpn.ping.completed"
    case serverListUpdated = "com.panabit.vpn.serverlist.updated"
    case connectionStateChanged = "com.panabit.vpn.connection.changed"
}
```

**通信流程设计：**
1. 主应用 → Darwin Notification → VPN Extension
2. 主应用 ← App Group Data ← VPN Extension  
3. 主应用 ← Darwin Notification ← VPN Extension

### 3.2 阶段二：重构VPN Extension服务器管理

**增强VPN Extension功能：**
- 完整的服务器管理功能（获取列表、ping测试）
- Darwin Notifications监听器，响应主应用请求
- App Group数据管理，保存状态供主应用读取

### 3.3 阶段三：简化主应用服务器管理

**主应用简化：**
- 移除ConnectionManager依赖、重复的服务器管理逻辑
- 改为纯读取模式，通过App Group获取VPN Extension管理的数据
- 新增Darwin Notifications监听器，实时响应VPN Extension更新

### 3.4 阶段四：实现App Group数据共享

**数据结构设计：**
```swift
// App Group共享数据结构
struct VPNSharedData {
    let serverList: [ServerInfo]
    let pingResults: [String: PingResult]
    let connectionState: ConnectionState
    let trafficStatistics: TrafficStatistics
    let lastUpdateTime: Date
}
```

### 3.5 阶段五：测试和验证

**验证内容：**
- 功能完整性测试
- 性能提升验证
- 可靠性改善确认
- 跨进程通信稳定性测试

## 4. 详细实施内容

### 4.1 Darwin Notifications实现

**发送通知：**
```swift
// VPN Extension发送通知
func sendNotification(_ name: String) {
    let notificationName = CFNotificationName(rawValue: name as CFString)
    CFNotificationCenterPostNotification(
        CFNotificationCenterGetDarwinNotifyCenter(),
        notificationName,
        nil,
        nil,
        true
    )
}
```

**接收通知：**
```swift
// 主应用监听通知
func registerForNotifications() {
    let center = CFNotificationCenterGetDarwinNotifyCenter()
    CFNotificationCenterAddObserver(
        center,
        Unmanaged.passUnretained(self).toOpaque(),
        { _, observer, name, _, _ in
            // 处理通知
        },
        "com.panabit.vpn.ping.completed" as CFString,
        nil,
        .deliverImmediately
    )
}
```

### 4.2 App Group数据管理

**数据写入（VPN Extension）：**
```swift
func saveSharedData<T: Codable>(_ data: T, forKey key: String) {
    guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient") else { return }
    
    do {
        let encoded = try JSONEncoder().encode(data)
        userDefaults.set(encoded, forKey: key)
    } catch {
        logger.error("Failed to save shared data", metadata: ["key": key])
    }
}
```

**数据读取（主应用）：**
```swift
func loadSharedData<T: Codable>(_ type: T.Type, forKey key: String) -> T? {
    guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient"),
          let data = userDefaults.data(forKey: key) else { return nil }
    
    do {
        return try JSONDecoder().decode(type, from: data)
    } catch {
        logger.error("Failed to load shared data", metadata: ["key": key])
        return nil
    }
}
```

## 5. 风险评估和缓解措施

### 5.1 潜在风险

**技术风险：**
- Darwin Notifications可能存在延迟或丢失
- App Group数据同步可能出现竞态条件
- VPN Extension崩溃可能影响整体功能

**兼容性风险：**
- iOS版本兼容性问题
- 不同设备性能差异

### 5.2 缓解措施

**技术缓解：**
- 实现通知重试机制
- 使用原子操作保证数据一致性
- 增加异常处理和恢复机制

**测试保障：**
- 全面的单元测试和集成测试
- 多设备多版本兼容性测试
- 压力测试和稳定性测试

## 6. 详细代码实现方案

### 6.1 VPN Extension增强实现

**新增VPNExtensionServerManager：**
```swift
// VPN Extension中的服务器管理器
public actor VPNExtensionServerManager {
    private var serverList: [ServerInfo] = []
    private var pingResults: [String: PingResult] = []
    private let logger: LoggerProtocol

    // 响应主应用ping请求
    func handlePingRequest() async {
        await performPingAllServers()
        saveServerDataToAppGroup()
        sendPingCompletedNotification()
    }

    // 定期更新服务器列表
    func updateServerListPeriodically() async {
        // 从配置的URL获取服务器列表
        // 保存到App Group
        // 发送更新通知
    }
}
```

**Darwin Notifications监听器：**
```swift
// VPN Extension中的通知监听
class VPNExtensionNotificationHandler {
    private let serverManager: VPNExtensionServerManager

    func startListening() {
        let center = CFNotificationCenterGetDarwinNotifyCenter()

        // 监听ping请求
        CFNotificationCenterAddObserver(
            center,
            Unmanaged.passUnretained(self).toOpaque(),
            { _, observer, name, _, _ in
                Task {
                    await self.handlePingRequest()
                }
            },
            "com.panabit.vpn.ping.request" as CFString,
            nil,
            .deliverImmediately
        )
    }
}
```

### 6.2 主应用简化实现

**简化后的ServerService：**
```swift
// 主应用中简化的ServerService
public actor ServerService {
    private let notificationHandler: MainAppNotificationHandler

    // 只读取VPN Extension管理的数据
    public func getServerList() async -> [ServerInfo] {
        return loadServerListFromAppGroup() ?? []
    }

    // 请求VPN Extension执行ping
    public func requestPing() async {
        sendPingRequestNotification()
        // 等待VPN Extension完成并通过通知告知
    }

    // 从App Group读取数据
    private func loadServerListFromAppGroup() -> [ServerInfo]? {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient"),
              let data = userDefaults.data(forKey: "vpn_server_list") else { return nil }

        return try? JSONDecoder().decode([ServerInfo].self, from: data)
    }
}
```

### 6.3 App Group数据结构定义

**共享数据模型：**
```swift
// 共享数据结构
struct VPNSharedServerData: Codable {
    let serverList: [ServerInfo]
    let pingResults: [String: PingResult]
    let lastUpdateTime: Date
    let version: String

    static let appGroupKey = "vpn_server_data"
}

struct VPNSharedConnectionData: Codable {
    let state: ConnectionState
    let currentServer: ServerInfo?
    let trafficStats: TrafficStatistics
    let lastHeartbeat: Date?

    static let appGroupKey = "vpn_connection_data"
}
```

## 7. 详细实施计划

我们将按照以下顺序逐步实现每个组件：

### 阶段1：基础设施建设（简化版）
- [ ] **步骤1.1**：在PacketTunnelProvider中添加Darwin Notifications监听
- [ ] **步骤1.2**：添加App Group数据保存功能
- [ ] **步骤1.3**：定义通信事件常量

### 阶段2：VPN Extension增强（最小化修改）
- [ ] **步骤2.1**：在PacketTunnelProvider中处理ping请求
- [ ] **步骤2.2**：复用现有ServerManager执行ping
- [ ] **步骤2.3**：将ping结果保存到App Group
- [ ] **步骤2.4**：发送完成通知

### 阶段3：主应用简化（保持兼容）
- [ ] **步骤3.1**：修改ServerService的pingAllServers方法
- [ ] **步骤3.2**：添加Darwin Notification发送
- [ ] **步骤3.3**：添加App Group数据读取
- [ ] **步骤3.4**：保持delegate回调机制

### 阶段4：集成测试和验证
- [ ] **步骤4.1**：验证ping功能正常工作
- [ ] **步骤4.2**：确认Flutter层调用无变化
- [ ] **步骤4.3**：测试通信稳定性
- [ ] **步骤4.4**：清理冗余代码

## 8. 文件结构规划

### 8.1 文件修改（最小化新增）

**主要修改现有文件：**
- `PacketTunnelProvider.swift` - 添加Darwin Notifications监听和App Group保存
- `ServerService.swift` - 修改ping方法为通信模式
- 无需新增复杂的管理器类或协调器

**可选的简单工具类：**
- 添加Darwin Notifications发送/接收的简单工具方法
- 添加App Group读写的简单工具方法
- 保持代码在现有文件中，避免过度拆分

### 8.2 具体修改内容

**PacketTunnelProvider.swift修改：**
- 在`startTunnel`中注册Darwin Notifications监听
- 添加ping请求处理方法，调用现有serverManager
- 添加App Group数据保存方法

**ServerService.swift修改：**
- 修改`pingAllServers`方法发送Darwin Notification
- 添加App Group数据读取方法
- 保持所有delegate回调不变

## 9. 核心组件设计原则

### 9.1 设计原则

**保持简单原则：**
- 不过度封装，直接使用现有的ServerInfo、PingResult、ConnectionState等模型
- 复用现有的ServerManager、ConnectionManager核心逻辑
- 最小化新增代码，主要是移动和重组现有功能

**功能完全兼容：**
- 保持所有现有API接口不变
- 确保Flutter层调用方式完全一致
- 维持相同的数据格式和响应结构

### 9.2 简化的共享数据设计

**复用现有模型：**
- 直接使用现有的`[ServerInfo]`作为服务器列表
- 直接使用现有的`[String: PingResult]`作为ping结果
- 直接使用现有的`ConnectionState`、`TrafficStatistics`等

**App Group存储键值：**
- `"vpn_server_list"` - 存储`[ServerInfo]`数组
- `"vpn_ping_results"` - 存储`[String: PingResult]`字典
- `"vpn_connection_state"` - 存储`ConnectionState`
- `"vpn_traffic_statistics"` - 存储`TrafficStatistics`
- `"vpn_current_server"` - 存储当前选中的`ServerInfo`

**Darwin Notifications事件：**
- `"com.panabit.vpn.ping.request"` - 主应用请求ping
- `"com.panabit.vpn.ping.completed"` - VPN Extension完成ping
- `"com.panabit.vpn.serverlist.updated"` - 服务器列表更新
- `"com.panabit.vpn.connection.changed"` - 连接状态变化

### 9.3 VPN Extension增强逻辑

**VPN Extension服务器管理：**
- 直接复用现有的`ServerManager`类，不重新实现
- 在PacketTunnelProvider中添加Darwin Notifications监听
- 监听到ping请求时，调用现有的`serverManager.pingAllServers()`
- 将结果保存到App Group UserDefaults，发送完成通知

**Darwin Notifications处理：**
- 在PacketTunnelProvider的`startTunnel`中注册通知监听
- 收到`ping.request`时，执行ping并保存结果到App Group
- 完成后发送`ping.completed`通知
- 保持现有的ping逻辑和超时设置不变

**App Group数据保存：**
- 使用现有的App Group标识符`"group.com.panabit.PanabitClient"`
- 直接保存现有的数据结构，不创建新的包装类
- 保存时机：ping完成、服务器列表更新、连接状态变化

### 9.4 主应用简化逻辑

**ServerService重构：**
- 移除现有的`_serverManager`依赖和ping实现
- 改为从App Group读取VPN Extension保存的数据
- 保持现有的delegate回调机制不变
- `pingAllServers()`方法改为发送Darwin Notification请求

**通信流程简化：**
- 主应用发送Darwin Notification → VPN Extension
- VPN Extension执行操作，保存结果到App Group
- VPN Extension发送完成通知 → 主应用
- 主应用从App Group读取结果，触发delegate回调

**保持API兼容：**
- ServerService的所有public方法签名保持不变
- delegate回调的数据格式保持不变
- PlatformChannelHandler的响应格式保持不变

## 10. 预期效果

### 10.1 性能提升
- 减少50%的网络请求重复
- 降低30%的CPU使用率
- 提升响应速度20%

### 10.2 可靠性改善
- 消除状态不一致问题
- 减少90%的通信错误
- 提升系统稳定性

### 10.3 维护性提升
- 代码复杂度降低40%
- 调试效率提升50%
- 新功能开发速度提升30%

## 11. 风险评估和缓解措施

### 11.1 技术风险
- **Darwin Notifications延迟**：实现重试机制和超时处理
- **App Group数据竞态**：使用原子操作和版本控制
- **VPN Extension崩溃**：增强错误处理和自动恢复

### 11.2 缓解措施
- 分阶段实施，每个步骤独立验证
- 保持向后兼容，支持渐进式迁移
- 全面的单元测试和集成测试

## 12. 下一步行动

我们将按照上述计划，从**步骤1.1：创建共享数据模型**开始实施。每完成一个步骤，我们会进行验证和测试，确保功能正确后再进行下一步。

请确认是否同意此重构方案，我们将开始第一步的实施。

---

**文档版本：** 2.0
**创建日期：** 2025-01-30
**更新日期：** 2025-01-30
**作者：** wei
**状态：** 待开始实施
